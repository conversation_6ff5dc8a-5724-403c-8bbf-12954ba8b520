/**
 * Status Indicator Component - Beautiful Status Badges
 */

import React from 'react';
import { Badge, Tag } from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';

const StatusIndicator = ({ 
  status, 
  text, 
  size = 'default',
  showIcon = true,
  variant = 'badge' // 'badge' or 'tag'
}) => {
  const getStatusConfig = (status) => {
    const configs = {
      online: {
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f',
        icon: <CheckCircleOutlined />,
        text: text || 'Online'
      },
      offline: {
        color: '#ff4d4f',
        bgColor: '#fff2f0',
        borderColor: '#ffccc7',
        icon: <CloseCircleOutlined />,
        text: text || 'Offline'
      },
      connecting: {
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#91d5ff',
        icon: <LoadingOutlined spin />,
        text: text || 'Connecting'
      },
      pending: {
        color: '#faad14',
        bgColor: '#fffbe6',
        borderColor: '#ffe58f',
        icon: <ClockCircleOutlined />,
        text: text || 'Pending'
      },
      running: {
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#91d5ff',
        icon: <LoadingOutlined spin />,
        text: text || 'Running'
      },
      completed: {
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f',
        icon: <CheckCircleOutlined />,
        text: text || 'Completed'
      },
      failed: {
        color: '#ff4d4f',
        bgColor: '#fff2f0',
        borderColor: '#ffccc7',
        icon: <CloseCircleOutlined />,
        text: text || 'Failed'
      },
      cancelled: {
        color: '#8c8c8c',
        bgColor: '#f5f5f5',
        borderColor: '#d9d9d9',
        icon: <PauseCircleOutlined />,
        text: text || 'Cancelled'
      },
      warning: {
        color: '#faad14',
        bgColor: '#fffbe6',
        borderColor: '#ffe58f',
        icon: <ExclamationCircleOutlined />,
        text: text || 'Warning'
      },
      error: {
        color: '#ff4d4f',
        bgColor: '#fff2f0',
        borderColor: '#ffccc7',
        icon: <CloseCircleOutlined />,
        text: text || 'Error'
      },
      success: {
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f',
        icon: <CheckCircleOutlined />,
        text: text || 'Success'
      },
      active: {
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f',
        icon: <CheckCircleOutlined />,
        text: text || 'Active'
      },
      inactive: {
        color: '#8c8c8c',
        bgColor: '#f5f5f5',
        borderColor: '#d9d9d9',
        icon: <PauseCircleOutlined />,
        text: text || 'Inactive'
      }
    };

    return configs[status] || configs.pending;
  };

  const config = getStatusConfig(status);

  if (variant === 'tag') {
    return (
      <Tag
        color={config.color}
        style={{
          borderRadius: '20px',
          padding: '4px 12px',
          fontSize: size === 'small' ? '11px' : '12px',
          fontWeight: 500,
          display: 'inline-flex',
          alignItems: 'center',
          gap: '6px',
          border: `1px solid ${config.borderColor}`,
          background: config.bgColor,
          color: config.color
        }}
      >
        {showIcon && config.icon}
        {config.text}
      </Tag>
    );
  }

  return (
    <div
      className="status-indicator"
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        gap: '8px',
        padding: size === 'small' ? '4px 8px' : '6px 12px',
        borderRadius: '20px',
        fontSize: size === 'small' ? '11px' : '12px',
        fontWeight: 500,
        background: config.bgColor,
        color: config.color,
        border: `1px solid ${config.borderColor}`,
        transition: 'all 0.3s ease'
      }}
    >
      {showIcon && (
        <span style={{ 
          fontSize: size === 'small' ? '12px' : '14px',
          display: 'flex',
          alignItems: 'center'
        }}>
          {config.icon}
        </span>
      )}
      <span>{config.text}</span>
    </div>
  );
};

// Preset status indicators for common use cases
export const OnlineStatus = (props) => <StatusIndicator status="online" {...props} />;
export const OfflineStatus = (props) => <StatusIndicator status="offline" {...props} />;
export const ConnectingStatus = (props) => <StatusIndicator status="connecting" {...props} />;
export const RunningStatus = (props) => <StatusIndicator status="running" {...props} />;
export const CompletedStatus = (props) => <StatusIndicator status="completed" {...props} />;
export const FailedStatus = (props) => <StatusIndicator status="failed" {...props} />;
export const PendingStatus = (props) => <StatusIndicator status="pending" {...props} />;

export default StatusIndicator;
