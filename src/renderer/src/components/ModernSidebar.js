/**
 * Modern Sidebar Component - Desktop Design
 */

import React, { useState, useEffect } from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Tooltip, Button } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  SearchOutlined,
  MessageOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  LogoutOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';

const { Sider } = Layout;

const ModernSidebar = ({ collapsed, setCollapsed }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState(3);

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
    },
    {
      key: '/profiles',
      icon: <UserOutlined />,
      label: 'Profile Manager',
    },
    {
      key: '/scraping',
      icon: <SearchOutlined />,
      label: 'Facebook Scraping',
    },
    {
      key: '/messaging',
      icon: <MessageOutlined />,
      label: 'Bulk Messaging',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile Settings',
    },
    {
      key: 'about',
      icon: <InfoCircleOutlined />,
      label: 'About Application',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Exit Application',
      onClick: () => {
        if (window.electronAPI) {
          window.electronAPI.closeApp();
        }
      }
    },
  ];

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={280}
      style={{
        overflow: 'auto',
        height: '100vh',
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        background: 'linear-gradient(180deg, #667eea 0%, #764ba2 100%)',
        boxShadow: '4px 0 20px rgba(0,0,0,0.1)',
        zIndex: 1000
      }}
    >
      {/* Logo Section */}
      <div className="sidebar-logo" style={{
        height: '80px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'rgba(255, 255, 255, 0.1)',
        margin: '16px',
        borderRadius: '16px',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <div className="logo-container" style={{
          display: 'flex',
          alignItems: 'center',
          gap: collapsed ? '0' : '12px'
        }}>
          {!collapsed ? (
            <>
              <div className="logo-icon" style={{
                fontSize: '36px',
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
              }}>
                📱
              </div>
              <div className="logo-text" style={{ color: 'white', lineHeight: 1.2 }}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', margin: 0 }}>
                  Facebook
                </div>
                <div style={{ fontSize: '14px', opacity: 0.9, margin: 0 }}>
                  Automation
                </div>
              </div>
            </>
          ) : (
            <div className="logo-icon-collapsed" style={{
              fontSize: '32px',
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
            }}>
              📱
            </div>
          )}
        </div>
      </div>

      {/* Collapse Toggle Button */}
      <div style={{ 
        padding: '0 16px', 
        marginBottom: '16px',
        display: 'flex',
        justifyContent: collapsed ? 'center' : 'flex-end'
      }}>
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={() => setCollapsed(!collapsed)}
          style={{
            fontSize: '16px',
            width: '40px',
            height: '40px',
            color: 'white',
            background: 'rgba(255, 255, 255, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        />
      </div>
      
      {/* Navigation Menu */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={({ key }) => navigate(key)}
        style={{
          background: 'transparent',
          border: 'none',
          padding: '0 16px'
        }}
        className="modern-sidebar-menu"
      />

      {/* User Profile Section */}
      <div style={{
        position: 'absolute',
        bottom: '20px',
        left: '16px',
        right: '16px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '16px',
        padding: '16px',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        {!collapsed ? (
          <div>
            {/* Notifications */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '12px'
            }}>
              <span style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
                NOTIFICATIONS
              </span>
              <Badge count={notifications} size="small">
                <BellOutlined style={{ color: 'white', fontSize: '16px' }} />
              </Badge>
            </div>

            {/* User Info */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="topRight"
              arrow
              trigger={['click']}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                padding: '8px',
                borderRadius: '12px',
                transition: 'background 0.3s',
                background: 'rgba(255, 255, 255, 0.1)'
              }}>
                <Avatar 
                  style={{ 
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    border: '2px solid rgba(255, 255, 255, 0.3)',
                    marginRight: '12px'
                  }}
                  icon={<UserOutlined />}
                />
                <div style={{ flex: 1 }}>
                  <div style={{ 
                    color: 'white', 
                    fontWeight: 'bold',
                    fontSize: '14px'
                  }}>
                    Administrator
                  </div>
                  <div style={{ 
                    color: 'rgba(255,255,255,0.7)', 
                    fontSize: '12px'
                  }}>
                    System Admin
                  </div>
                </div>
                <ThunderboltOutlined style={{ 
                  color: 'rgba(255,255,255,0.6)',
                  fontSize: '16px'
                }} />
              </div>
            </Dropdown>
          </div>
        ) : (
          <div style={{ textAlign: 'center' }}>
            <Tooltip title="Notifications" placement="right">
              <Badge count={notifications} size="small">
                <BellOutlined style={{ 
                  color: 'white', 
                  fontSize: '20px',
                  marginBottom: '12px'
                }} />
              </Badge>
            </Tooltip>
            
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="topRight"
              arrow
              trigger={['click']}
            >
              <Avatar 
                style={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  cursor: 'pointer'
                }}
                icon={<UserOutlined />}
                size="large"
              />
            </Dropdown>
          </div>
        )}
      </div>

      {/* Custom Styles */}
      <style jsx>{`
        .modern-sidebar-menu .ant-menu-item {
          margin: 6px 0 !important;
          border-radius: 12px !important;
          height: 48px !important;
          line-height: 48px !important;
          transition: all 0.3s ease !important;
          border: 1px solid transparent !important;
        }

        .modern-sidebar-menu .ant-menu-item:hover {
          background: rgba(255, 255, 255, 0.15) !important;
          transform: translateX(4px);
          border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        .modern-sidebar-menu .ant-menu-item-selected {
          background: rgba(255, 255, 255, 0.25) !important;
          border: 1px solid rgba(255, 255, 255, 0.3) !important;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }

        .modern-sidebar-menu .ant-menu-item-selected::after {
          display: none !important;
        }

        .modern-sidebar-menu .ant-menu-item .ant-menu-item-icon {
          font-size: 18px;
          margin-right: 12px;
        }

        .modern-sidebar-menu .ant-menu-item-selected .ant-menu-item-icon {
          color: white !important;
        }

        .modern-sidebar-menu .ant-menu-item-selected span {
          color: white !important;
          font-weight: bold;
        }

        .modern-sidebar-menu .ant-menu-item span {
          font-size: 14px;
          font-weight: 500;
        }

        /* Scrollbar Styling */
        .ant-layout-sider::-webkit-scrollbar {
          width: 6px;
        }

        .ant-layout-sider::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }

        .ant-layout-sider::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.3);
          border-radius: 3px;
        }

        .ant-layout-sider::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.5);
        }
      `}</style>
    </Sider>
  );
};

export default ModernSidebar;
