/**
 * Modern Button Component - Beautiful Gradient Buttons
 */

import React from 'react';
import { Button } from 'antd';

const ModernButton = ({ 
  children,
  variant = 'primary',
  size = 'default',
  gradient = true,
  gradientColors,
  className = '',
  style = {},
  ...props 
}) => {
  const getGradientColors = () => {
    if (gradientColors) return gradientColors;
    
    switch (variant) {
      case 'primary':
        return ['#667eea', '#764ba2'];
      case 'success':
        return ['#56ab2f', '#a8e6cf'];
      case 'warning':
        return ['#f093fb', '#f5576c'];
      case 'danger':
        return ['#ff416c', '#ff4b2b'];
      case 'info':
        return ['#4facfe', '#00f2fe'];
      default:
        return ['#667eea', '#764ba2'];
    }
  };

  const colors = getGradientColors();
  
  const buttonStyle = gradient ? {
    background: `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`,
    border: 'none',
    borderRadius: size === 'large' ? '12px' : '8px',
    color: 'white',
    fontWeight: 500,
    transition: 'all 0.3s ease',
    boxShadow: `0 4px 12px rgba(${hexToRgb(colors[0])}, 0.3)`,
    ...style
  } : {
    borderRadius: size === 'large' ? '12px' : '8px',
    fontWeight: 500,
    transition: 'all 0.3s ease',
    ...style
  };

  const hoverStyle = gradient ? {
    background: `linear-gradient(135deg, ${adjustBrightness(colors[0], -10)} 0%, ${adjustBrightness(colors[1], -10)} 100%)`,
    transform: 'translateY(-1px)',
    boxShadow: `0 6px 16px rgba(${hexToRgb(colors[0])}, 0.4)`
  } : {};

  return (
    <Button
      className={`modern-button ${className}`}
      style={buttonStyle}
      size={size}
      onMouseEnter={(e) => {
        if (gradient) {
          Object.assign(e.target.style, hoverStyle);
        }
      }}
      onMouseLeave={(e) => {
        if (gradient) {
          Object.assign(e.target.style, buttonStyle);
        }
      }}
      {...props}
    >
      {children}
    </Button>
  );
};

// Helper functions
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? 
    `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : 
    '102, 126, 234';
}

function adjustBrightness(hex, percent) {
  const num = parseInt(hex.replace("#", ""), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}

export default ModernButton;
