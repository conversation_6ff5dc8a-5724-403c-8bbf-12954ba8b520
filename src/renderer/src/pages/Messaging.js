/**
 * Messaging Page Component - Complete Implementation
 */

import React, { useState, useEffect } from 'react';
import {
  Card, Form, Input, Select, Button, Space, Upload, Table, Progress,
  message, Row, Col, InputNumber, Switch, Tag, Modal, Divider, Tooltip,
  Alert, List, Statistic
} from 'antd';
import {
  MessageOutlined, UploadOutlined, PlayCircleOutlined, StopOutlined,
  EyeOutlined, DownloadOutlined, DeleteOutlined, ReloadOutlined,
  UserOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const { Option } = Select;
const { TextArea } = Input;

const Messaging = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [profiles, setProfiles] = useState([]);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [activeTask, setActiveTask] = useState(null);
  const [taskProgress, setTaskProgress] = useState({});
  const [workerStats, setWorkerStats] = useState({});
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [selectedTaskResults, setSelectedTaskResults] = useState(null);

  useEffect(() => {
    loadInitialData();

    // Set up polling for active tasks
    const interval = setInterval(() => {
      if (activeTask) {
        pollTaskStatus(activeTask);
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [activeTask]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Load profiles and tasks
      const [profilesData, tasksData] = await Promise.all([
        apiService.getProfiles(),
        apiService.get('/api/messaging/')
      ]);

      setProfiles(profilesData.filter(p => p.facebook_logged_in));
      setTasks(tasksData);

    } catch (error) {
      message.error('Failed to load data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const result = await apiService.post('/api/messaging/upload-recipients', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      setUploadedFile(result);
      message.success(`Uploaded ${result.total_recipients} recipients successfully`);

      return false; // Prevent default upload behavior
    } catch (error) {
      message.error('Failed to upload file: ' + error.message);
      return false;
    }
  };

  const handleStartMessaging = async (values) => {
    try {
      setLoading(true);

      const config = {
        name: values.name,
        sender_profile_ids: values.sender_profile_ids,
        recipient_list_file: uploadedFile?.file_path,
        message_template: values.message_template,
        message_type: values.message_type || 'text',
        image_paths: values.image_paths,
        concurrent_threads: values.concurrent_threads || 1,
        messages_per_account_min: values.messages_per_account_min || 1,
        messages_per_account_max: values.messages_per_account_max || 10,
        delay_between_messages_min: values.delay_between_messages_min || 5,
        delay_between_messages_max: values.delay_between_messages_max || 15,
        avoid_duplicate_uids: values.avoid_duplicate_uids !== false,
        randomize_message: values.randomize_message === true
      };

      const result = await apiService.post('/api/messaging/start', { config });

      setActiveTask(result.task_id);
      message.success('Messaging task started successfully');

      // Reset form and reload tasks
      form.resetFields();
      setUploadedFile(null);
      loadInitialData();

    } catch (error) {
      message.error('Failed to start messaging: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const pollTaskStatus = async (taskId) => {
    try {
      const [status, workers] = await Promise.all([
        apiService.get(`/api/messaging/status/${taskId}`),
        apiService.get(`/api/messaging/worker-stats/${taskId}`).catch(() => null)
      ]);

      setTaskProgress(prev => ({ ...prev, [taskId]: status }));
      if (workers) {
        setWorkerStats(prev => ({ ...prev, [taskId]: workers }));
      }

      // Stop polling if task is completed
      if (['completed', 'failed', 'cancelled'].includes(status.status)) {
        setActiveTask(null);
        loadInitialData();
      }

    } catch (error) {
      console.error('Failed to poll task status:', error);
    }
  };

  const handleStopTask = async (taskId) => {
    try {
      await apiService.post(`/api/messaging/stop/${taskId}`);
      message.success('Task stopped successfully');
      setActiveTask(null);
      loadInitialData();
    } catch (error) {
      message.error('Failed to stop task: ' + error.message);
    }
  };

  const handleViewResults = async (taskId) => {
    try {
      const results = await apiService.get(`/api/messaging/results/${taskId}`);
      setSelectedTaskResults(results);
      setPreviewModalVisible(true);
    } catch (error) {
      message.error('Failed to load results: ' + error.message);
    }
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      pending: { color: 'default', text: 'Pending' },
      running: { color: 'processing', text: 'Running' },
      completed: { color: 'success', text: 'Completed' },
      failed: { color: 'error', text: 'Failed' },
      cancelled: { color: 'default', text: 'Cancelled' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getMessageStatusIcon = (status) => {
    switch (status) {
      case 'sent':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'skipped':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const taskColumns = [
    {
      title: 'Task Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_, record) => {
        const progress = taskProgress[record.task_id];
        if (progress) {
          return (
            <div>
              <Progress
                percent={progress.progress}
                size="small"
                status={progress.status === 'failed' ? 'exception' : 'active'}
              />
              <div style={{ fontSize: '12px', color: '#666' }}>
                {progress.current_step}
              </div>
            </div>
          );
        }
        return <Progress percent={0} size="small" />;
      }
    },
    {
      title: 'Recipients',
      dataIndex: 'total_recipients',
      key: 'total_recipients'
    },
    {
      title: 'Sent/Failed/Skipped',
      key: 'stats',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <span style={{ color: '#52c41a' }}>✓ {record.messages_sent || 0}</span>
          <span style={{ color: '#ff4d4f' }}>✗ {record.messages_failed || 0}</span>
          <span style={{ color: '#faad14' }}>⏸ {record.messages_skipped || 0}</span>
        </Space>
      )
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'running' && (
            <Tooltip title="Stop Task">
              <Button
                icon={<StopOutlined />}
                size="small"
                danger
                onClick={() => handleStopTask(record.task_id)}
              />
            </Tooltip>
          )}

          <Tooltip title="View Results">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewResults(record.task_id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <h1>Bulk Messaging</h1>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadInitialData}
            loading={loading}
          >
            Refresh
          </Button>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Create New Task */}
        <Col xs={24} lg={12}>
          <Card title="Create Messaging Task">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartMessaging}
            >
              <Form.Item
                name="name"
                label="Task Name"
                rules={[{ required: true, message: 'Please enter task name' }]}
              >
                <Input placeholder="Enter task name" />
              </Form.Item>

              <Form.Item
                name="sender_profile_ids"
                label="Sender Profiles"
                rules={[{ required: true, message: 'Please select sender profiles' }]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select profiles to send messages from"
                  optionFilterProp="children"
                >
                  {profiles.map(profile => (
                    <Option key={profile.id} value={profile.id}>
                      {profile.name} ({profile.facebook_username})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label="Recipient List">
                <Upload
                  beforeUpload={handleFileUpload}
                  accept=".csv,.xlsx,.xls"
                  showUploadList={false}
                >
                  <Button icon={<UploadOutlined />}>
                    Upload Recipients (CSV/Excel)
                  </Button>
                </Upload>

                {uploadedFile && (
                  <Alert
                    message={`${uploadedFile.total_recipients} recipients loaded from ${uploadedFile.list_name}`}
                    type="success"
                    style={{ marginTop: 8 }}
                    showIcon
                  />
                )}
              </Form.Item>

              <Form.Item
                name="message_template"
                label="Message Template"
                rules={[{ required: true, message: 'Please enter message template' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Enter your message template. Use {name} for recipient name."
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="concurrent_threads"
                    label="Concurrent Threads"
                    initialValue={1}
                  >
                    <InputNumber min={1} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="message_type"
                    label="Message Type"
                    initialValue="text"
                  >
                    <Select>
                      <Option value="text">Text Only</Option>
                      <Option value="image">Image Only</Option>
                      <Option value="text_with_image">Text + Image</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Button
                type="primary"
                htmlType="submit"
                icon={<MessageOutlined />}
                loading={loading}
                disabled={!uploadedFile}
                block
              >
                Start Messaging
              </Button>
            </Form>
          </Card>
        </Col>

        {/* Active Task Status */}
        <Col xs={24} lg={12}>
          {activeTask && taskProgress[activeTask] && (
            <Card title="Active Task Status">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Progress
                  percent={taskProgress[activeTask].progress}
                  status={taskProgress[activeTask].status === 'failed' ? 'exception' : 'active'}
                />

                <div>
                  <strong>Status:</strong> {getStatusTag(taskProgress[activeTask].status)}
                </div>

                <div>
                  <strong>Current Step:</strong> {taskProgress[activeTask].current_step}
                </div>

                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="Total Recipients"
                      value={taskProgress[activeTask].total_recipients}
                      prefix={<UserOutlined />}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="Messages Sent"
                      value={taskProgress[activeTask].messages_sent}
                      prefix={<CheckCircleOutlined />}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="Failed"
                      value={taskProgress[activeTask].messages_failed}
                      prefix={<CloseCircleOutlined />}
                      valueStyle={{ color: '#cf1322' }}
                    />
                  </Col>
                </Row>

                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={() => handleStopTask(activeTask)}
                  block
                >
                  Stop Task
                </Button>
              </Space>
            </Card>
          )}
        </Col>
      </Row>

      {/* Tasks History */}
      <Card title="Messaging Tasks" style={{ marginTop: 24 }}>
        <Table
          columns={taskColumns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} tasks`
          }}
        />
      </Card>

      {/* Results Modal */}
      <Modal
        title="Messaging Results"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            Close
          </Button>
        ]}
      >
        {selectedTaskResults && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic title="Total Recipients" value={selectedTaskResults.total_recipients} />
              </Col>
              <Col span={6}>
                <Statistic title="Messages Sent" value={selectedTaskResults.messages_sent} />
              </Col>
              <Col span={6}>
                <Statistic title="Failed" value={selectedTaskResults.messages_failed} />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Success Rate"
                  value={selectedTaskResults.success_rate}
                  suffix="%"
                />
              </Col>
            </Row>

            <Table
              size="small"
              columns={[
                {
                  title: 'Status',
                  dataIndex: 'status',
                  key: 'status',
                  render: (status) => (
                    <Space>
                      {getMessageStatusIcon(status)}
                      {status}
                    </Space>
                  )
                },
                {
                  title: 'Recipient',
                  dataIndex: 'recipient_name',
                  key: 'recipient_name',
                  render: (name, record) => name || record.recipient_uid
                },
                {
                  title: 'Message',
                  dataIndex: 'message_content',
                  key: 'message_content',
                  render: (content) => content.length > 50 ? content.substring(0, 50) + '...' : content
                },
                {
                  title: 'Sent At',
                  dataIndex: 'sent_at',
                  key: 'sent_at',
                  render: (date) => date ? new Date(date).toLocaleString() : '-'
                }
              ]}
              dataSource={selectedTaskResults.messages}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Messaging;
