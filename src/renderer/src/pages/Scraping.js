/**
 * Scraping Page Component - Complete Implementation
 */

import React, { useState, useEffect } from 'react';
import {
  Card, Form, Input, Select, Button, Space, Table, Progress,
  message, Row, Col, InputNumber, Checkbox, Tag, Modal, Tooltip,
  Alert, Statistic
} from 'antd';
import {
  SearchOutlined, PlayCircleOutlined, StopOutlined, EyeOutlined,
  DownloadOutlined, ReloadOutlined, UserOutlined, CheckCircleOutlined,
  CloseCircleOutlined, ExportOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const { Option } = Select;

const Scraping = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [profiles, setProfiles] = useState([]);
  const [activeTask, setActiveTask] = useState(null);
  const [taskProgress, setTaskProgress] = useState({});
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [selectedTaskResults, setSelectedTaskResults] = useState(null);
  const [exportHistory, setExportHistory] = useState([]);

  useEffect(() => {
    loadInitialData();

    // Set up polling for active tasks
    const interval = setInterval(() => {
      if (activeTask) {
        pollTaskStatus(activeTask);
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [activeTask]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Load profiles, tasks, and export history
      const [profilesData, tasksData, exportsData] = await Promise.all([
        apiService.getProfiles(),
        apiService.get('/api/scraping/'),
        apiService.get('/api/scraping/exports/history').catch(() => ({ exports: [] }))
      ]);

      setProfiles(profilesData.filter(p => p.facebook_logged_in));
      setTasks(tasksData);
      setExportHistory(exportsData.exports || []);

    } catch (error) {
      message.error('Failed to load data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleStartScraping = async (values) => {
    try {
      setLoading(true);

      const config = {
        target_url: values.target_url,
        scraping_types: values.scraping_types || ['all'],
        max_results: values.max_results || 1000,
        profile_id: values.profile_id
      };

      const result = await apiService.post('/api/scraping/start', { config });

      setActiveTask(result.task_id);
      message.success('Scraping task started successfully');

      // Reset form and reload tasks
      form.resetFields();
      loadInitialData();

    } catch (error) {
      message.error('Failed to start scraping: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const pollTaskStatus = async (taskId) => {
    try {
      const status = await apiService.get(`/api/scraping/status/${taskId}`);
      setTaskProgress(prev => ({ ...prev, [taskId]: status }));

      // Stop polling if task is completed
      if (['completed', 'failed', 'cancelled'].includes(status.status)) {
        setActiveTask(null);
        loadInitialData();
      }

    } catch (error) {
      console.error('Failed to poll task status:', error);
    }
  };

  const handleStopTask = async (taskId) => {
    try {
      await apiService.post(`/api/scraping/stop/${taskId}`);
      message.success('Task stopped successfully');
      setActiveTask(null);
      loadInitialData();
    } catch (error) {
      message.error('Failed to stop task: ' + error.message);
    }
  };

  const handleViewResults = async (taskId) => {
    try {
      const results = await apiService.get(`/api/scraping/results/${taskId}`);
      setSelectedTaskResults(results);
      setPreviewModalVisible(true);
    } catch (error) {
      message.error('Failed to load results: ' + error.message);
    }
  };

  const handleExportResults = async (taskId, format = 'excel') => {
    try {
      const result = await apiService.get(`/api/scraping/export/${taskId}?format=${format}`);

      if (result.success) {
        message.success(`Data exported successfully to ${result.filename}`);
        loadInitialData(); // Reload to update export history
      } else {
        message.error('Export failed: ' + result.error);
      }
    } catch (error) {
      message.error('Failed to export results: ' + error.message);
    }
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      pending: { color: 'default', text: 'Pending' },
      running: { color: 'processing', text: 'Running' },
      completed: { color: 'success', text: 'Completed' },
      failed: { color: 'error', text: 'Failed' },
      cancelled: { color: 'default', text: 'Cancelled' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const taskColumns = [
    {
      title: 'Target URL',
      dataIndex: 'target_url',
      key: 'target_url',
      render: (url) => (
        <Tooltip title={url}>
          <a href={url} target="_blank" rel="noopener noreferrer">
            {url.length > 50 ? url.substring(0, 50) + '...' : url}
          </a>
        </Tooltip>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_, record) => {
        const progress = taskProgress[record.task_id];
        if (progress) {
          return (
            <div>
              <Progress
                percent={progress.progress}
                size="small"
                status={progress.status === 'failed' ? 'exception' : 'active'}
              />
              <div style={{ fontSize: '12px', color: '#666' }}>
                {progress.current_step}
              </div>
            </div>
          );
        }
        return <Progress percent={record.progress || 0} size="small" />;
      }
    },
    {
      title: 'Found/Scraped',
      key: 'results',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <span>Found: {record.total_found || 0}</span>
          <span>Scraped: {record.total_scraped || 0}</span>
        </Space>
      )
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'running' && (
            <Tooltip title="Stop Task">
              <Button
                icon={<StopOutlined />}
                size="small"
                danger
                onClick={() => handleStopTask(record.task_id)}
              />
            </Tooltip>
          )}

          <Tooltip title="View Results">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewResults(record.task_id)}
              disabled={!record.total_scraped}
            />
          </Tooltip>

          {record.status === 'completed' && record.total_scraped > 0 && (
            <Tooltip title="Export to Excel">
              <Button
                icon={<ExportOutlined />}
                size="small"
                type="primary"
                onClick={() => handleExportResults(record.task_id)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <h1>Facebook Scraping</h1>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadInitialData}
            loading={loading}
          >
            Refresh
          </Button>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Create New Task */}
        <Col xs={24} lg={12}>
          <Card title="Create Scraping Task">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartScraping}
            >
              <Form.Item
                name="target_url"
                label="Facebook Post URL"
                rules={[
                  { required: true, message: 'Please enter Facebook post URL' },
                  { type: 'url', message: 'Please enter a valid URL' }
                ]}
              >
                <Input placeholder="https://www.facebook.com/..." />
              </Form.Item>

              <Form.Item
                name="profile_id"
                label="Profile to Use"
                rules={[{ required: true, message: 'Please select a profile' }]}
              >
                <Select placeholder="Select a logged-in profile">
                  {profiles.map(profile => (
                    <Option key={profile.id} value={profile.id}>
                      {profile.name} ({profile.facebook_username})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="scraping_types"
                label="What to Scrape"
                initialValue={['all']}
              >
                <Checkbox.Group>
                  <Row>
                    <Col span={24}>
                      <Checkbox value="all">All (Comments + Likes + Shares)</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="comments">Comments</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="likes">Likes</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="shares">Shares</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <Form.Item
                name="max_results"
                label="Maximum Results"
                initialValue={1000}
              >
                <InputNumber
                  min={1}
                  max={10000}
                  style={{ width: '100%' }}
                  placeholder="Maximum number of users to scrape"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                  block
                >
                  Start Scraping
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* Active Task Status */}
        <Col xs={24} lg={12}>
          {activeTask && taskProgress[activeTask] && (
            <Card title="Active Task Status">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Progress
                  percent={taskProgress[activeTask].progress}
                  status={taskProgress[activeTask].status === 'failed' ? 'exception' : 'active'}
                />

                <div>
                  <strong>Status:</strong> {getStatusTag(taskProgress[activeTask].status)}
                </div>

                <div>
                  <strong>Current Step:</strong> {taskProgress[activeTask].current_step}
                </div>

                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="Total Found"
                      value={taskProgress[activeTask].total_found}
                      prefix={<UserOutlined />}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="Total Scraped"
                      value={taskProgress[activeTask].total_scraped}
                      prefix={<CheckCircleOutlined />}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                </Row>

                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={() => handleStopTask(activeTask)}
                  block
                >
                  Stop Task
                </Button>
              </Space>
            </Card>
          )}
        </Col>
      </Row>

      {/* Tasks History */}
      <Card title="Scraping Tasks" style={{ marginTop: 24 }}>
        <Table
          columns={taskColumns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} tasks`
          }}
        />
      </Card>

      {/* Results Modal */}
      <Modal
        title="Scraping Results"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            Close
          </Button>
        ]}
      >
        {selectedTaskResults && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic title="Total Users" value={selectedTaskResults.total_users} />
              </Col>
              <Col span={6}>
                <Statistic title="Comments" value={selectedTaskResults.users_by_type.comment || 0} />
              </Col>
              <Col span={6}>
                <Statistic title="Likes" value={selectedTaskResults.users_by_type.like || 0} />
              </Col>
              <Col span={6}>
                <Statistic title="Shares" value={selectedTaskResults.users_by_type.share || 0} />
              </Col>
            </Row>

            <Table
              size="small"
              columns={[
                {
                  title: 'UID',
                  dataIndex: 'facebook_uid',
                  key: 'facebook_uid',
                  width: 120
                },
                {
                  title: 'Name',
                  dataIndex: 'full_name',
                  key: 'full_name'
                },
                {
                  title: 'Type',
                  dataIndex: 'interaction_type',
                  key: 'interaction_type',
                  render: (type) => <Tag>{type}</Tag>
                },
                {
                  title: 'Content',
                  dataIndex: 'interaction_content',
                  key: 'interaction_content',
                  render: (content) => content && content.length > 50 ? content.substring(0, 50) + '...' : content || '-'
                },
                {
                  title: 'Scraped At',
                  dataIndex: 'scraped_at',
                  key: 'scraped_at',
                  render: (date) => new Date(date).toLocaleString()
                }
              ]}
              dataSource={selectedTaskResults.users}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Scraping;
