/**
 * Settings Page Component
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, Form, Input, InputNumber, Switch, Button, Space, 
  Divider, message, Row, Col, Select, Slider 
} from 'antd';
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons';

const { Option } = Select;

const Settings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    // Browser Settings
    maxConcurrentBrowsers: 5,
    browserTimeout: 30000,
    headlessMode: false,
    
    // Scraping Settings
    maxScrapingWorkers: 3,
    scrapingDelayMin: 2,
    scrapingDelayMax: 5,
    autoExportResults: true,
    
    // Messaging Settings
    maxMessagingWorkers: 5,
    messageDelayMin: 5,
    messageDelayMax: 15,
    avoidDuplicateUIDs: true,
    randomizeMessages: false,
    
    // Rate Limiting
    requestsPerMinute: 30,
    requestsPerHour: 500,
    
    // System Settings
    enableLogging: true,
    logLevel: 'info',
    autoCleanupLogs: true,
    maxLogFileSize: 100, // MB
    
    // UI Settings
    theme: 'light',
    language: 'en',
    autoRefreshInterval: 30 // seconds
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // Load settings from electron store
      const savedSettings = await window.electronAPI.store.get('app_settings');
      if (savedSettings) {
        setSettings({ ...settings, ...savedSettings });
        form.setFieldsValue({ ...settings, ...savedSettings });
      } else {
        form.setFieldsValue(settings);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      form.setFieldsValue(settings);
    }
  };

  const handleSave = async (values) => {
    try {
      setLoading(true);
      
      // Save to electron store
      await window.electronAPI.store.set('app_settings', values);
      
      setSettings(values);
      message.success('Settings saved successfully');
      
    } catch (error) {
      console.error('Failed to save settings:', error);
      message.error('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.setFieldsValue(settings);
    message.info('Settings reset to last saved values');
  };

  const handleDefaults = () => {
    const defaultSettings = {
      maxConcurrentBrowsers: 5,
      browserTimeout: 30000,
      headlessMode: false,
      maxScrapingWorkers: 3,
      scrapingDelayMin: 2,
      scrapingDelayMax: 5,
      autoExportResults: true,
      maxMessagingWorkers: 5,
      messageDelayMin: 5,
      messageDelayMax: 15,
      avoidDuplicateUIDs: true,
      randomizeMessages: false,
      requestsPerMinute: 30,
      requestsPerHour: 500,
      enableLogging: true,
      logLevel: 'info',
      autoCleanupLogs: true,
      maxLogFileSize: 100,
      theme: 'light',
      language: 'en',
      autoRefreshInterval: 30
    };
    
    form.setFieldsValue(defaultSettings);
    message.info('Settings reset to default values');
  };

  return (
    <div>
      <h1 style={{ marginBottom: 24 }}>Settings</h1>
      
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={settings}
      >
        {/* Browser Settings */}
        <Card title="Browser Settings" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="maxConcurrentBrowsers"
                label="Max Concurrent Browsers"
                tooltip="Maximum number of browser instances that can run simultaneously"
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="browserTimeout"
                label="Browser Timeout (ms)"
                tooltip="Timeout for browser operations in milliseconds"
              >
                <InputNumber min={5000} max={120000} step={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="headlessMode"
            label="Headless Mode"
            valuePropName="checked"
            tooltip="Run browsers in headless mode (not recommended for Facebook automation)"
          >
            <Switch />
          </Form.Item>
        </Card>

        {/* Scraping Settings */}
        <Card title="Scraping Settings" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="maxScrapingWorkers"
                label="Max Scraping Workers"
                tooltip="Number of concurrent scraping workers"
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="scrapingDelayMin"
                label="Min Delay (seconds)"
                tooltip="Minimum delay between scraping actions"
              >
                <InputNumber min={1} max={30} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="scrapingDelayMax"
                label="Max Delay (seconds)"
                tooltip="Maximum delay between scraping actions"
              >
                <InputNumber min={1} max={60} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="autoExportResults"
            label="Auto Export Results"
            valuePropName="checked"
            tooltip="Automatically export scraping results to Excel"
          >
            <Switch />
          </Form.Item>
        </Card>

        {/* Messaging Settings */}
        <Card title="Messaging Settings" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="maxMessagingWorkers"
                label="Max Messaging Workers"
                tooltip="Number of concurrent messaging workers"
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="messageDelayMin"
                label="Min Delay (seconds)"
                tooltip="Minimum delay between messages"
              >
                <InputNumber min={1} max={60} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="messageDelayMax"
                label="Max Delay (seconds)"
                tooltip="Maximum delay between messages"
              >
                <InputNumber min={1} max={120} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="avoidDuplicateUIDs"
                label="Avoid Duplicate UIDs"
                valuePropName="checked"
                tooltip="Prevent sending messages to the same UID from multiple accounts"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="randomizeMessages"
                label="Randomize Messages"
                valuePropName="checked"
                tooltip="Add random variations to message templates"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Rate Limiting */}
        <Card title="Rate Limiting" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="requestsPerMinute"
                label="Requests Per Minute"
                tooltip="Maximum requests per minute per profile"
              >
                <Slider min={10} max={100} marks={{ 10: '10', 30: '30', 60: '60', 100: '100' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="requestsPerHour"
                label="Requests Per Hour"
                tooltip="Maximum requests per hour per profile"
              >
                <Slider min={100} max={2000} marks={{ 100: '100', 500: '500', 1000: '1K', 2000: '2K' }} />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* System Settings */}
        <Card title="System Settings" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableLogging"
                label="Enable Logging"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="logLevel"
                label="Log Level"
              >
                <Select>
                  <Option value="debug">Debug</Option>
                  <Option value="info">Info</Option>
                  <Option value="warning">Warning</Option>
                  <Option value="error">Error</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="autoCleanupLogs"
                label="Auto Cleanup Logs"
                valuePropName="checked"
                tooltip="Automatically delete old log files"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxLogFileSize"
                label="Max Log File Size (MB)"
              >
                <InputNumber min={10} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* UI Settings */}
        <Card title="UI Settings" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="theme"
                label="Theme"
              >
                <Select>
                  <Option value="light">Light</Option>
                  <Option value="dark">Dark</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="language"
                label="Language"
              >
                <Select>
                  <Option value="en">English</Option>
                  <Option value="vi">Tiếng Việt</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="autoRefreshInterval"
                label="Auto Refresh (seconds)"
                tooltip="Auto refresh interval for dashboard and status"
              >
                <InputNumber min={10} max={300} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Action Buttons */}
        <Card>
          <Space>
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              htmlType="submit"
              loading={loading}
            >
              Save Settings
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleReset}
            >
              Reset
            </Button>
            <Button onClick={handleDefaults}>
              Restore Defaults
            </Button>
          </Space>
        </Card>
      </Form>
    </div>
  );
};

export default Settings;
