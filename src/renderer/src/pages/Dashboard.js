/**
 * Dashboard Page Component
 */

import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Progress, List, Tag, Button, Space } from 'antd';
import {
  UserOutlined,
  SearchOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalProfiles: 0,
    activeProfiles: 0,
    runningTasks: 0,
    totalScrapedUsers: 0,
    totalMessagesSent: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load profiles
      const profiles = await apiService.getProfiles();
      const activeProfiles = profiles.filter(p => p.status === 'active' || p.status === 'logged_in');
      
      setStats({
        totalProfiles: profiles.length,
        activeProfiles: activeProfiles.length,
        runningTasks: 0, // TODO: Get from API
        totalScrapedUsers: 0, // TODO: Get from API
        totalMessagesSent: 0 // TODO: Get from API
      });

      // Mock recent activities
      setRecentActivities([
        {
          id: 1,
          type: 'profile_created',
          message: 'Profile "Test Profile 1" created successfully',
          timestamp: new Date(),
          status: 'success'
        },
        {
          id: 2,
          type: 'scraping_completed',
          message: 'Scraping task completed - 150 users found',
          timestamp: new Date(Date.now() - 3600000),
          status: 'success'
        }
      ]);

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'profile_created':
        return <UserOutlined style={{ color: '#52c41a' }} />;
      case 'scraping_completed':
        return <SearchOutlined style={{ color: '#1890ff' }} />;
      case 'messaging_completed':
        return <MessageOutlined style={{ color: '#722ed1' }} />;
      default:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    }
  };

  const getStatusTag = (status) => {
    switch (status) {
      case 'success':
        return <Tag color="success">Success</Tag>;
      case 'warning':
        return <Tag color="warning">Warning</Tag>;
      case 'error':
        return <Tag color="error">Error</Tag>;
      default:
        return <Tag color="default">Info</Tag>;
    }
  };

  return (
    <div>
      <h1 style={{ marginBottom: 24 }}>Dashboard</h1>
      
      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Profiles"
              value={stats.totalProfiles}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Active Profiles"
              value={stats.activeProfiles}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Running Tasks"
              value={stats.runningTasks}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Messages Sent"
              value={stats.totalMessagesSent}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* System Status */}
        <Col xs={24} lg={12}>
          <Card title="System Status" loading={loading}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <div style={{ marginBottom: 8 }}>
                  <span>CPU Usage</span>
                  <span style={{ float: 'right' }}>45%</span>
                </div>
                <Progress percent={45} status="active" />
              </div>
              
              <div>
                <div style={{ marginBottom: 8 }}>
                  <span>Memory Usage</span>
                  <span style={{ float: 'right' }}>62%</span>
                </div>
                <Progress percent={62} status="active" />
              </div>
              
              <div>
                <div style={{ marginBottom: 8 }}>
                  <span>Active Browsers</span>
                  <span style={{ float: 'right' }}>{stats.activeProfiles}/5</span>
                </div>
                <Progress 
                  percent={(stats.activeProfiles / 5) * 100} 
                  status={stats.activeProfiles >= 5 ? "exception" : "active"} 
                />
              </div>
            </Space>
          </Card>
        </Col>

        {/* Recent Activities */}
        <Col xs={24} lg={12}>
          <Card 
            title="Recent Activities" 
            loading={loading}
            extra={
              <Button type="link" onClick={loadDashboardData}>
                Refresh
              </Button>
            }
          >
            <List
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getActivityIcon(item.type)}
                    title={
                      <Space>
                        <span>{item.message}</span>
                        {getStatusTag(item.status)}
                      </Space>
                    }
                    description={item.timestamp.toLocaleString()}
                  />
                </List.Item>
              )}
              locale={{ emptyText: 'No recent activities' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="Quick Actions">
            <Space wrap>
              <Button type="primary" icon={<UserOutlined />}>
                Create New Profile
              </Button>
              <Button icon={<SearchOutlined />}>
                Start Scraping
              </Button>
              <Button icon={<MessageOutlined />}>
                Send Messages
              </Button>
              <Button icon={<ExclamationCircleOutlined />}>
                View Logs
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
