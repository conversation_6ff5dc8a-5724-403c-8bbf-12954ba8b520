"""
Facebook Automation Desktop - Backend API Server
Main FastAPI application entry point
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager

# Import our modules
from app.core.config import settings
from app.core.database import init_db
from app.api.routes import profiles, scraping, messaging, system
from app.core.logger import setup_logger

# Setup logger
logger = setup_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Facebook Automation Backend...")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    # Initialize crawl4ai
    try:
        from crawl4ai import AsyncWebCrawler
        # Test crawl4ai initialization
        async with AsyncWebCrawler() as crawler:
            logger.info("Crawl4ai initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize crawl4ai: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Facebook Automation Backend...")


# Create FastAPI app
app = FastAPI(
    title="Facebook Automation API",
    description="Backend API for Facebook automation desktop application",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(profiles.router, prefix="/api/profiles", tags=["profiles"])
app.include_router(scraping.router, prefix="/api/scraping", tags=["scraping"])
app.include_router(messaging.router, prefix="/api/messaging", tags=["messaging"])
app.include_router(system.router, prefix="/api/system", tags=["system"])

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Facebook Automation Backend is running"}


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Facebook Automation Backend API",
        "version": "1.0.0",
        "docs": "/docs"
    }


if __name__ == "__main__":
    import uvicorn
    
    # Run the server
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
