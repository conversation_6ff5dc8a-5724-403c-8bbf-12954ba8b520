"""
Scraping API routes
"""

from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Optional

from ...core.database import get_db
from ...core.logger import setup_logger
from ...models.scraping import (
    ScrapingTaskCreate, ScrapingTaskResponse, ScrapingResults,
    ScrapingTask, ScrapingConfig, ExportRequest
)
from ...services.scraping_task_manager import scraping_task_manager

logger = setup_logger(__name__)
router = APIRouter()


@router.get("/", response_model=List[ScrapingTaskResponse])
async def get_scraping_tasks(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Get all scraping tasks"""
    try:
        result = await db.execute(
            select(ScrapingTask).offset(skip).limit(limit).order_by(ScrapingTask.created_at.desc())
        )
        tasks = result.scalars().all()

        return [ScrapingTaskResponse.from_attributes(task) for task in tasks]

    except Exception as e:
        logger.error(f"Failed to get scraping tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get scraping tasks: {str(e)}"
        )


@router.post("/start", response_model=dict)
async def start_scraping(
    task_data: ScrapingTaskCreate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Start a new scraping task"""
    try:
        # Create task
        task_id = await scraping_task_manager.create_scraping_task(task_data.config)

        # Start task in background
        background_tasks.add_task(scraping_task_manager.start_scraping_task, task_id)

        return {
            "task_id": task_id,
            "message": "Scraping task started successfully",
            "status": "pending"
        }

    except Exception as e:
        logger.error(f"Failed to start scraping task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start scraping task: {str(e)}"
        )


@router.get("/status/{task_id}")
async def get_scraping_status(task_id: str):
    """Get scraping task status"""
    try:
        status_info = await scraping_task_manager.get_task_status(task_id)

        if not status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )

        return status_info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get scraping status for {task_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get scraping status: {str(e)}"
        )


@router.post("/stop/{task_id}")
async def stop_scraping(task_id: str):
    """Stop a running scraping task"""
    try:
        success = await scraping_task_manager.stop_scraping_task(task_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found or already stopped"
            )

        return {"message": "Scraping task stopped successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop scraping task {task_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop scraping task: {str(e)}"
        )


@router.get("/results/{task_id}", response_model=ScrapingResults)
async def get_scraping_results(task_id: str):
    """Get scraping task results"""
    try:
        results = await scraping_task_manager.get_task_results(task_id)

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found or no results available"
            )

        return results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get scraping results for {task_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get scraping results: {str(e)}"
        )


@router.get("/export/{task_id}")
async def export_scraping_results(
    task_id: str,
    format: str = "excel",
    include_fields: Optional[str] = None
):
    """Export scraping results to file"""
    try:
        from ...services.export_service import export_service

        # Parse include_fields if provided
        fields_list = None
        if include_fields:
            fields_list = [field.strip() for field in include_fields.split(',')]

        # Export data
        result = await export_service.export_scraping_results(
            task_id=task_id,
            format=format,
            include_fields=fields_list
        )

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result['error']
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export results for {task_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export results: {str(e)}"
        )


@router.get("/exports/history")
async def get_export_history(limit: int = 50):
    """Get export history"""
    try:
        from ...services.export_service import export_service

        history = await export_service.get_export_history(limit)
        return {"exports": history}

    except Exception as e:
        logger.error(f"Failed to get export history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get export history: {str(e)}"
        )


@router.delete("/exports/{filename}")
async def delete_export_file(filename: str):
    """Delete an export file"""
    try:
        from ...services.export_service import export_service

        success = await export_service.delete_export_file(filename)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Export file not found"
            )

        return {"message": "Export file deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete export file {filename}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete export file: {str(e)}"
        )
