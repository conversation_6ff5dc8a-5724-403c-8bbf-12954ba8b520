"""
Profile management API routes
"""

from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from typing import List, Optional

from ...core.database import get_db
from ...core.logger import setup_logger
from ...models.profile import (
    Profile, ProfileCreate, ProfileUpdate, ProfileResponse, 
    FacebookLoginRequest, ProfileTestResult, ProfileStatus
)
from ...services.profile_manager import AntidetectProfileManager

logger = setup_logger(__name__)
router = APIRouter()

# Initialize profile manager
profile_manager = AntidetectProfileManager()


@router.get("/", response_model=List[ProfileResponse])
async def get_profiles(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Get all profiles"""
    try:
        result = await db.execute(
            select(Profile).offset(skip).limit(limit).order_by(Profile.created_at.desc())
        )
        profiles = result.scalars().all()
        
        return [ProfileResponse.from_db_model(profile) for profile in profiles]
        
    except Exception as e:
        logger.error(f"Failed to get profiles: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get profiles: {str(e)}"
        )


@router.get("/{profile_id}", response_model=ProfileResponse)
async def get_profile(profile_id: int, db: AsyncSession = Depends(get_db)):
    """Get a specific profile"""
    try:
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        
        return ProfileResponse.from_db_model(profile)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get profile {profile_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get profile: {str(e)}"
        )


@router.post("/", response_model=ProfileResponse)
async def create_profile(
    profile_data: ProfileCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new profile"""
    try:
        # Check if profile name already exists
        result = await db.execute(select(Profile).where(Profile.name == profile_data.name))
        existing_profile = result.scalar_one_or_none()
        
        if existing_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Profile name already exists"
            )
        
        # Create profile using profile manager
        creation_result = await profile_manager.create_profile({
            'name': profile_data.name,
            'proxy_config': profile_data.proxy_config.dict()
        })
        
        if not creation_result['success']:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=creation_result['message']
            )
        
        # Save to database
        db_profile = Profile(
            name=profile_data.name,
            profile_path=creation_result['profile_path'],
            proxy_type=profile_data.proxy_config.type,
            proxy_host=profile_data.proxy_config.host,
            proxy_port=profile_data.proxy_config.port,
            proxy_username=profile_data.proxy_config.username,
            proxy_password=profile_data.proxy_config.password,
            fingerprint_data=creation_result['fingerprint'],
            status=ProfileStatus.CREATED
        )
        
        db.add(db_profile)
        await db.commit()
        await db.refresh(db_profile)
        
        logger.info(f"Profile created successfully: {profile_data.name}")
        return ProfileResponse.from_db_model(db_profile)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create profile: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create profile: {str(e)}"
        )


@router.put("/{profile_id}", response_model=ProfileResponse)
async def update_profile(
    profile_id: int,
    profile_data: ProfileUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update a profile"""
    try:
        # Get existing profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        
        # Update fields
        update_data = {}
        if profile_data.name is not None:
            update_data['name'] = profile_data.name
        if profile_data.proxy_config is not None:
            update_data.update({
                'proxy_type': profile_data.proxy_config.type,
                'proxy_host': profile_data.proxy_config.host,
                'proxy_port': profile_data.proxy_config.port,
                'proxy_username': profile_data.proxy_config.username,
                'proxy_password': profile_data.proxy_config.password
            })
        if profile_data.status is not None:
            update_data['status'] = profile_data.status
        
        if update_data:
            await db.execute(
                update(Profile).where(Profile.id == profile_id).values(**update_data)
            )
            await db.commit()
            
            # Refresh profile
            await db.refresh(profile)
        
        logger.info(f"Profile updated successfully: {profile_id}")
        return ProfileResponse.from_db_model(profile)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update profile {profile_id}: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update profile: {str(e)}"
        )


@router.delete("/{profile_id}")
async def delete_profile(profile_id: int, db: AsyncSession = Depends(get_db)):
    """Delete a profile"""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        
        # Delete profile directory
        await profile_manager.delete_profile(profile.profile_path)
        
        # Delete from database
        await db.execute(delete(Profile).where(Profile.id == profile_id))
        await db.commit()
        
        logger.info(f"Profile deleted successfully: {profile_id}")
        return {"message": "Profile deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete profile {profile_id}: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete profile: {str(e)}"
        )


@router.post("/{profile_id}/test", response_model=ProfileTestResult)
async def test_profile(profile_id: int, db: AsyncSession = Depends(get_db)):
    """Test profile connectivity"""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        
        # Build proxy config
        proxy_config = {
            'type': profile.proxy_type,
            'host': profile.proxy_host,
            'port': profile.proxy_port,
            'username': profile.proxy_username,
            'password': profile.proxy_password
        }
        
        # Test profile
        test_result = await profile_manager.test_profile(profile.profile_path, proxy_config)
        
        # Update profile status based on test result
        new_status = ProfileStatus.ACTIVE if test_result['success'] else ProfileStatus.ERROR
        error_message = None if test_result['success'] else test_result.get('error')
        
        await db.execute(
            update(Profile)
            .where(Profile.id == profile_id)
            .values(status=new_status, error_message=error_message)
        )
        await db.commit()
        
        return ProfileTestResult(**test_result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to test profile {profile_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test profile: {str(e)}"
        )


@router.post("/{profile_id}/login")
async def login_facebook(
    profile_id: int,
    credentials: FacebookLoginRequest,
    db: AsyncSession = Depends(get_db)
):
    """Login to Facebook with profile"""
    try:
        # Get profile
        result = await db.execute(select(Profile).where(Profile.id == profile_id))
        profile = result.scalar_one_or_none()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        
        # Attempt Facebook login
        login_result = await profile_manager.login_facebook(
            profile.profile_path,
            credentials.dict()
        )
        
        if login_result['success']:
            # Update profile status
            await db.execute(
                update(Profile)
                .where(Profile.id == profile_id)
                .values(
                    facebook_logged_in=True,
                    facebook_username=credentials.username,
                    status=ProfileStatus.LOGGED_IN
                )
            )
            await db.commit()
        
        return login_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to login Facebook for profile {profile_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to login Facebook: {str(e)}"
        )
