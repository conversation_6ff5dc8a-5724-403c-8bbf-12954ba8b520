"""
System API routes - Performance monitoring and statistics
"""

from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any

from ...core.logger import setup_logger
from ...services.performance_optimizer import performance_optimizer

logger = setup_logger(__name__)
router = APIRouter()


@router.get("/stats")
async def get_system_stats():
    """Get system performance statistics"""
    try:
        stats = performance_optimizer.get_performance_stats()
        
        return {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",  # You can use datetime.now().isoformat()
            "performance": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get system stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system stats: {str(e)}"
        )


@router.post("/cleanup")
async def cleanup_system():
    """Clean up system resources"""
    try:
        await performance_optimizer.cleanup()
        
        return {"message": "System cleanup completed successfully"}
        
    except Exception as e:
        logger.error(f"Failed to cleanup system: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cleanup system: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """System health check"""
    try:
        # Basic health checks
        health_status = {
            "status": "healthy",
            "checks": {
                "database": True,  # You can implement actual DB health check
                "redis": True,     # You can implement actual Redis health check
                "performance_optimizer": True
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )
