"""
Configuration settings for the Facebook Automation Backend
"""

import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # API Settings
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Database Settings
    DATABASE_URL: str = "sqlite+aiosqlite:///./facebook_automation.db"
    
    # Redis Settings (for caching and task queue)
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Application Paths
    BASE_DIR: Path = Path(__file__).parent.parent.parent
    DATA_DIR: Path = BASE_DIR / "data"
    PROFILES_DIR: Path = DATA_DIR / "profiles"
    EXPORTS_DIR: Path = DATA_DIR / "exports"
    LOGS_DIR: Path = DATA_DIR / "logs"
    
    # Browser Settings
    BROWSER_TIMEOUT: int = 30000  # 30 seconds
    MAX_CONCURRENT_BROWSERS: int = 5
    
    # Scraping Settings
    MAX_SCRAPING_WORKERS: int = 3
    SCRAPING_DELAY_MIN: int = 2
    SCRAPING_DELAY_MAX: int = 5
    
    # Messaging Settings
    MAX_MESSAGING_WORKERS: int = 5
    MESSAGE_DELAY_MIN: int = 5
    MESSAGE_DELAY_MAX: int = 15
    
    # Rate Limiting
    REQUESTS_PER_MINUTE: int = 30
    REQUESTS_PER_HOUR: int = 500
    
    class Config:
        env_file = ".env"
        case_sensitive = True
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Create directories if they don't exist
        self.DATA_DIR.mkdir(exist_ok=True)
        self.PROFILES_DIR.mkdir(exist_ok=True)
        self.EXPORTS_DIR.mkdir(exist_ok=True)
        self.LOGS_DIR.mkdir(exist_ok=True)


# Global settings instance
settings = Settings()
