"""
Scraping Task Manager - Manages Facebook scraping tasks
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from ..core.config import settings
from ..core.logger import setup_logger
from ..core.database import AsyncSessionLocal
from ..models.scraping import (
    ScrapingTask, ScrapedUser, ScrapingTaskStatus, 
    ScrapingType, ScrapingConfig
)
from .facebook_scraper import FacebookScraper

logger = setup_logger(__name__)


class ScrapingTaskManager:
    """
    Manages Facebook scraping tasks with progress tracking
    """
    
    def __init__(self):
        self.facebook_scraper = FacebookScraper()
        self.active_tasks = {}  # task_id -> task_info
        self.task_progress = {}  # task_id -> progress_info
        
    async def create_scraping_task(self, config: ScrapingConfig) -> str:
        """Create a new scraping task"""
        try:
            task_id = str(uuid.uuid4())
            
            async with AsyncSessionLocal() as db:
                # Create database record
                db_task = ScrapingTask(
                    task_id=task_id,
                    profile_id=config.profile_id,
                    target_url=config.target_url,
                    scraping_types=[t.value for t in config.scraping_types],
                    max_results=config.max_results,
                    status=ScrapingTaskStatus.PENDING
                )
                
                db.add(db_task)
                await db.commit()
                await db.refresh(db_task)
                
                # Store task info
                self.active_tasks[task_id] = {
                    'db_id': db_task.id,
                    'config': config,
                    'created_at': datetime.now(),
                    'crawler': None
                }
                
                self.task_progress[task_id] = {
                    'status': ScrapingTaskStatus.PENDING,
                    'progress': 0.0,
                    'total_found': 0,
                    'total_scraped': 0,
                    'current_step': 'Initializing...',
                    'errors': []
                }
                
                logger.info(f"Created scraping task {task_id} for profile {config.profile_id}")
                return task_id
                
        except Exception as e:
            logger.error(f"Failed to create scraping task: {e}")
            raise
    
    async def start_scraping_task(self, task_id: str) -> bool:
        """Start a scraping task"""
        try:
            if task_id not in self.active_tasks:
                raise ValueError(f"Task {task_id} not found")
            
            task_info = self.active_tasks[task_id]
            config = task_info['config']
            
            # Update status to running
            await self._update_task_status(task_id, ScrapingTaskStatus.RUNNING, started_at=datetime.now())
            
            # Start scraping in background
            asyncio.create_task(self._run_scraping_task(task_id))
            
            logger.info(f"Started scraping task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start scraping task {task_id}: {e}")
            await self._update_task_status(task_id, ScrapingTaskStatus.FAILED, error_message=str(e))
            return False
    
    async def _run_scraping_task(self, task_id: str):
        """Run the actual scraping task"""
        try:
            task_info = self.active_tasks[task_id]
            config = task_info['config']

            # Update progress
            await self._update_progress(task_id, 10.0, "Creating scraping session...")

            # Use performance optimizer for better resource management
            from .performance_optimizer import performance_optimizer

            # Create scraping session
            crawler = await self.facebook_scraper.create_scraping_session(
                config.profile_id,
                task_id
            )
            
            if not crawler:
                raise Exception("Failed to create scraping session")
            
            task_info['crawler'] = crawler
            
            # Update progress
            await self._update_progress(task_id, 20.0, "Navigating to Facebook post...")
            
            # Scrape the post
            scraping_results = await self.facebook_scraper.scrape_facebook_post(
                crawler=crawler,
                post_url=config.target_url,
                scraping_types=config.scraping_types,
                max_results=config.max_results
            )
            
            # Update progress
            await self._update_progress(task_id, 80.0, "Processing scraped data...")
            
            # Save results to database
            await self._save_scraping_results(task_id, scraping_results)
            
            # Update progress
            await self._update_progress(task_id, 100.0, "Scraping completed successfully")
            
            # Mark as completed
            await self._update_task_status(
                task_id, 
                ScrapingTaskStatus.COMPLETED, 
                completed_at=datetime.now(),
                total_found=scraping_results['total_found'],
                total_scraped=scraping_results['total_found']
            )
            
            logger.info(f"Scraping task {task_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Scraping task {task_id} failed: {e}")
            await self._update_task_status(
                task_id, 
                ScrapingTaskStatus.FAILED, 
                error_message=str(e)
            )
            await self._update_progress(task_id, 0.0, f"Failed: {str(e)}")
            
        finally:
            # Clean up
            await self.facebook_scraper.close_scraping_session(task_id)
            if task_id in self.active_tasks and 'crawler' in self.active_tasks[task_id]:
                self.active_tasks[task_id]['crawler'] = None
    
    async def _save_scraping_results(self, task_id: str, results: Dict[str, Any]):
        """Save scraping results to database"""
        try:
            task_info = self.active_tasks[task_id]
            db_id = task_info['db_id']
            
            async with AsyncSessionLocal() as db:
                # Save all scraped users
                all_users = []
                
                # Process comments
                for comment in results.get('comments', []):
                    user = ScrapedUser(
                        task_id=db_id,
                        facebook_uid=comment['facebook_uid'],
                        full_name=comment['full_name'],
                        profile_url=comment['profile_url'],
                        interaction_type=comment['interaction_type'],
                        interaction_content=comment['interaction_content'],
                        interaction_timestamp=comment['interaction_timestamp'],
                        profile_picture_url=comment.get('profile_picture_url'),
                        metadata=comment.get('metadata', {})
                    )
                    all_users.append(user)
                
                # Process likes
                for like in results.get('likes', []):
                    user = ScrapedUser(
                        task_id=db_id,
                        facebook_uid=like['facebook_uid'],
                        full_name=like['full_name'],
                        profile_url=like['profile_url'],
                        interaction_type=like['interaction_type'],
                        interaction_content=like['interaction_content'],
                        interaction_timestamp=like['interaction_timestamp'],
                        metadata=like.get('metadata', {})
                    )
                    all_users.append(user)
                
                # Process shares
                for share in results.get('shares', []):
                    user = ScrapedUser(
                        task_id=db_id,
                        facebook_uid=share['facebook_uid'],
                        full_name=share['full_name'],
                        profile_url=share['profile_url'],
                        interaction_type=share['interaction_type'],
                        interaction_content=share['interaction_content'],
                        interaction_timestamp=share['interaction_timestamp'],
                        metadata=share.get('metadata', {})
                    )
                    all_users.append(user)
                
                # Bulk insert
                if all_users:
                    db.add_all(all_users)
                    await db.commit()
                
                logger.info(f"Saved {len(all_users)} scraped users for task {task_id}")
                
        except Exception as e:
            logger.error(f"Failed to save scraping results for task {task_id}: {e}")
            raise
    
    async def _update_task_status(
        self, 
        task_id: str, 
        status: ScrapingTaskStatus, 
        **kwargs
    ):
        """Update task status in database"""
        try:
            if task_id not in self.active_tasks:
                return
            
            task_info = self.active_tasks[task_id]
            db_id = task_info['db_id']
            
            update_data = {'status': status}
            update_data.update(kwargs)
            
            async with AsyncSessionLocal() as db:
                await db.execute(
                    update(ScrapingTask)
                    .where(ScrapingTask.id == db_id)
                    .values(**update_data)
                )
                await db.commit()
            
            # Update local progress
            if task_id in self.task_progress:
                self.task_progress[task_id]['status'] = status
                
        except Exception as e:
            logger.error(f"Failed to update task status for {task_id}: {e}")
    
    async def _update_progress(self, task_id: str, progress: float, step: str):
        """Update task progress"""
        try:
            if task_id in self.task_progress:
                self.task_progress[task_id].update({
                    'progress': progress,
                    'current_step': step
                })
            
            # Also update database
            if task_id in self.active_tasks:
                task_info = self.active_tasks[task_id]
                db_id = task_info['db_id']
                
                async with AsyncSessionLocal() as db:
                    await db.execute(
                        update(ScrapingTask)
                        .where(ScrapingTask.id == db_id)
                        .values(progress=progress)
                    )
                    await db.commit()
                    
        except Exception as e:
            logger.error(f"Failed to update progress for task {task_id}: {e}")
    
    async def stop_scraping_task(self, task_id: str) -> bool:
        """Stop a running scraping task"""
        try:
            if task_id not in self.active_tasks:
                return False
            
            # Close scraping session
            await self.facebook_scraper.close_scraping_session(task_id)
            
            # Update status
            await self._update_task_status(task_id, ScrapingTaskStatus.CANCELLED)
            
            # Clean up
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            if task_id in self.task_progress:
                del self.task_progress[task_id]
            
            logger.info(f"Stopped scraping task {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop scraping task {task_id}: {e}")
            return False
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status and progress"""
        try:
            if task_id in self.task_progress:
                return self.task_progress[task_id]
            
            # Try to get from database
            async with AsyncSessionLocal() as db:
                result = await db.execute(
                    select(ScrapingTask).where(ScrapingTask.task_id == task_id)
                )
                task = result.scalar_one_or_none()
                
                if task:
                    return {
                        'status': task.status,
                        'progress': task.progress,
                        'total_found': task.total_found,
                        'total_scraped': task.total_scraped,
                        'current_step': 'Task not active',
                        'errors': []
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get task status for {task_id}: {e}")
            return None
    
    async def get_task_results(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task results"""
        try:
            async with AsyncSessionLocal() as db:
                # Get task
                task_result = await db.execute(
                    select(ScrapingTask).where(ScrapingTask.task_id == task_id)
                )
                task = task_result.scalar_one_or_none()
                
                if not task:
                    return None
                
                # Get scraped users
                users_result = await db.execute(
                    select(ScrapedUser).where(ScrapedUser.task_id == task.id)
                )
                users = users_result.scalars().all()
                
                # Group by interaction type
                users_by_type = {}
                all_users = []
                
                for user in users:
                    interaction_type = user.interaction_type
                    if interaction_type not in users_by_type:
                        users_by_type[interaction_type] = 0
                    users_by_type[interaction_type] += 1
                    
                    all_users.append({
                        'id': user.id,
                        'facebook_uid': user.facebook_uid,
                        'full_name': user.full_name,
                        'profile_url': user.profile_url,
                        'interaction_type': user.interaction_type,
                        'interaction_content': user.interaction_content,
                        'interaction_timestamp': user.interaction_timestamp,
                        'profile_picture_url': user.profile_picture_url,
                        'scraped_at': user.scraped_at
                    })
                
                return {
                    'task_id': task_id,
                    'status': task.status,
                    'total_users': len(all_users),
                    'users_by_type': users_by_type,
                    'users': all_users
                }
                
        except Exception as e:
            logger.error(f"Failed to get task results for {task_id}: {e}")
            return None
    
    async def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """Clean up old completed tasks"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            # Remove from active tasks
            to_remove = []
            for task_id, task_info in self.active_tasks.items():
                if (task_info['created_at'] < cutoff_time and 
                    task_id in self.task_progress and 
                    self.task_progress[task_id]['status'] in [
                        ScrapingTaskStatus.COMPLETED, 
                        ScrapingTaskStatus.FAILED, 
                        ScrapingTaskStatus.CANCELLED
                    ]):
                    to_remove.append(task_id)
            
            for task_id in to_remove:
                await self.facebook_scraper.close_scraping_session(task_id)
                del self.active_tasks[task_id]
                if task_id in self.task_progress:
                    del self.task_progress[task_id]
            
            logger.info(f"Cleaned up {len(to_remove)} old tasks")
            
        except Exception as e:
            logger.error(f"Failed to cleanup completed tasks: {e}")


# Global instance
scraping_task_manager = ScrapingTaskManager()
