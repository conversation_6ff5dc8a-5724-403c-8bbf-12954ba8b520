"""
Export Service - Export scraped data to various formats
"""

import pandas as pd
import csv
import json
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..core.config import settings
from ..core.logger import setup_logger
from ..core.database import AsyncSessionLocal
from ..models.scraping import ScrapingTask, ScrapedUser

logger = setup_logger(__name__)


class ExportService:
    """
    Service for exporting scraped data to various formats
    """
    
    def __init__(self):
        self.export_dir = settings.EXPORTS_DIR
        self.export_dir.mkdir(exist_ok=True)
    
    async def export_scraping_results(
        self, 
        task_id: str, 
        format: str = "excel",
        include_fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Export scraping results to specified format"""
        try:
            # Get task and scraped data
            async with AsyncSessionLocal() as db:
                # Get task
                task_result = await db.execute(
                    select(ScrapingTask).where(ScrapingTask.task_id == task_id)
                )
                task = task_result.scalar_one_or_none()
                
                if not task:
                    raise ValueError(f"Task {task_id} not found")
                
                # Get scraped users
                users_result = await db.execute(
                    select(ScrapedUser).where(ScrapedUser.task_id == task.id)
                )
                users = users_result.scalars().all()
                
                if not users:
                    raise ValueError(f"No data found for task {task_id}")
            
            # Prepare data for export
            export_data = await self._prepare_export_data(users, include_fields)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"facebook_scraping_{task_id[:8]}_{timestamp}"
            
            # Export based on format
            if format.lower() == "excel":
                file_path = await self._export_to_excel(export_data, base_filename)
            elif format.lower() == "csv":
                file_path = await self._export_to_csv(export_data, base_filename)
            elif format.lower() == "json":
                file_path = await self._export_to_json(export_data, base_filename)
            else:
                raise ValueError(f"Unsupported export format: {format}")
            
            return {
                "success": True,
                "file_path": str(file_path),
                "filename": file_path.name,
                "total_records": len(export_data),
                "format": format,
                "created_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to export scraping results: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_path": None,
                "total_records": 0
            }
    
    async def _prepare_export_data(
        self, 
        users: List[ScrapedUser], 
        include_fields: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Prepare scraped data for export"""
        
        # Default fields to include
        default_fields = [
            'stt', 'facebook_uid', 'full_name', 'gender', 'profile_url',
            'interaction_type', 'interaction_content', 'interaction_timestamp',
            'profile_picture_url', 'scraped_at'
        ]
        
        fields_to_include = include_fields if include_fields else default_fields
        
        export_data = []
        
        for index, user in enumerate(users, 1):
            # Extract gender from metadata or profile analysis
            gender = await self._extract_gender(user)
            
            record = {}
            
            # Add fields based on selection
            if 'stt' in fields_to_include:
                record['STT'] = index
            
            if 'facebook_uid' in fields_to_include:
                record['UID'] = user.facebook_uid
            
            if 'full_name' in fields_to_include:
                record['Họ và tên'] = user.full_name or ""
            
            if 'gender' in fields_to_include:
                record['Giới tính'] = gender
            
            if 'profile_url' in fields_to_include:
                record['Link Facebook'] = user.profile_url or ""
            
            if 'interaction_type' in fields_to_include:
                record['Loại tương tác'] = self._translate_interaction_type(user.interaction_type)
            
            if 'interaction_content' in fields_to_include:
                record['Nội dung tương tác'] = user.interaction_content or ""
            
            if 'interaction_timestamp' in fields_to_include:
                record['Thời gian tương tác'] = user.interaction_timestamp.strftime("%Y-%m-%d %H:%M:%S") if user.interaction_timestamp else ""
            
            if 'profile_picture_url' in fields_to_include:
                record['Ảnh đại diện'] = user.profile_picture_url or ""
            
            if 'scraped_at' in fields_to_include:
                record['Thời gian thu thập'] = user.scraped_at.strftime("%Y-%m-%d %H:%M:%S")
            
            export_data.append(record)
        
        return export_data
    
    async def _extract_gender(self, user: ScrapedUser) -> str:
        """Extract gender information from user data"""
        try:
            # Check metadata first
            if user.metadata and isinstance(user.metadata, dict):
                gender = user.metadata.get('gender')
                if gender:
                    return gender
            
            # Try to determine gender from name (basic implementation)
            # In a real implementation, you might use a more sophisticated approach
            full_name = user.full_name or ""
            
            # Vietnamese name patterns (very basic)
            male_indicators = ['anh', 'ông', 'thầy', 'bác', 'chú']
            female_indicators = ['chị', 'cô', 'bà', 'em gái']
            
            name_lower = full_name.lower()
            
            for indicator in male_indicators:
                if indicator in name_lower:
                    return "Nam"
            
            for indicator in female_indicators:
                if indicator in name_lower:
                    return "Nữ"
            
            # Default to unknown
            return "Không xác định"
            
        except Exception as e:
            logger.warning(f"Failed to extract gender for user {user.facebook_uid}: {e}")
            return "Không xác định"
    
    def _translate_interaction_type(self, interaction_type: str) -> str:
        """Translate interaction type to Vietnamese"""
        translations = {
            'comment': 'Bình luận',
            'like': 'Thích',
            'love': 'Yêu thích',
            'haha': 'Haha',
            'wow': 'Wow',
            'sad': 'Buồn',
            'angry': 'Tức giận',
            'share': 'Chia sẻ'
        }
        
        return translations.get(interaction_type.lower(), interaction_type)
    
    async def _export_to_excel(self, data: List[Dict[str, Any]], base_filename: str) -> Path:
        """Export data to Excel format"""
        try:
            file_path = self.export_dir / f"{base_filename}.xlsx"
            
            # Create DataFrame
            df = pd.DataFrame(data)
            
            # Create Excel writer with formatting
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # Write main data
                df.to_excel(writer, sheet_name='Dữ liệu Facebook', index=False)
                
                # Get workbook and worksheet
                workbook = writer.book
                worksheet = writer.sheets['Dữ liệu Facebook']
                
                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
                
                # Add summary sheet
                summary_data = {
                    'Thông tin': ['Tổng số bản ghi', 'Thời gian xuất', 'Định dạng'],
                    'Giá trị': [len(data), datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 'Excel']
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Tóm tắt', index=False)
            
            logger.info(f"Exported {len(data)} records to Excel: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to export to Excel: {e}")
            raise
    
    async def _export_to_csv(self, data: List[Dict[str, Any]], base_filename: str) -> Path:
        """Export data to CSV format"""
        try:
            file_path = self.export_dir / f"{base_filename}.csv"
            
            if not data:
                raise ValueError("No data to export")
            
            # Write CSV with UTF-8 BOM for Excel compatibility
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                writer.writerows(data)
            
            logger.info(f"Exported {len(data)} records to CSV: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to export to CSV: {e}")
            raise
    
    async def _export_to_json(self, data: List[Dict[str, Any]], base_filename: str) -> Path:
        """Export data to JSON format"""
        try:
            file_path = self.export_dir / f"{base_filename}.json"
            
            export_object = {
                'metadata': {
                    'total_records': len(data),
                    'exported_at': datetime.now().isoformat(),
                    'format': 'json'
                },
                'data': data
            }
            
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_object, jsonfile, ensure_ascii=False, indent=2)
            
            logger.info(f"Exported {len(data)} records to JSON: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to export to JSON: {e}")
            raise
    
    async def get_export_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get export history"""
        try:
            export_files = []
            
            # Scan export directory
            for file_path in self.export_dir.glob("facebook_scraping_*"):
                if file_path.is_file():
                    stat = file_path.stat()
                    export_files.append({
                        'filename': file_path.name,
                        'file_path': str(file_path),
                        'size': stat.st_size,
                        'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'format': file_path.suffix[1:].upper()
                    })
            
            # Sort by creation time (newest first)
            export_files.sort(key=lambda x: x['created_at'], reverse=True)
            
            return export_files[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get export history: {e}")
            return []
    
    async def delete_export_file(self, filename: str) -> bool:
        """Delete an export file"""
        try:
            file_path = self.export_dir / filename
            
            if file_path.exists() and file_path.is_file():
                file_path.unlink()
                logger.info(f"Deleted export file: {filename}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete export file {filename}: {e}")
            return False


# Global instance
export_service = ExportService()
