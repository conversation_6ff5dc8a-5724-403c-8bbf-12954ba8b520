"""
Anti-Detection Service for Facebook Messaging
"""

import asyncio
import random
import time
import json
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..core.config import settings
from ..core.logger import setup_logger

logger = setup_logger(__name__)


class BehaviorPattern(Enum):
    """Human behavior patterns"""
    CONSERVATIVE = "conservative"  # Very careful, long delays
    NORMAL = "normal"             # Average human behavior
    ACTIVE = "active"             # More active user
    RANDOM = "random"             # Randomized pattern


@dataclass
class HumanBehaviorConfig:
    """Configuration for human-like behavior"""
    typing_speed_wpm: Tuple[int, int] = (30, 60)  # Words per minute range
    pause_between_words: Tuple[float, float] = (0.1, 0.3)  # Seconds
    pause_between_sentences: Tuple[float, float] = (0.5, 1.5)  # Seconds
    thinking_pause: Tuple[float, float] = (1.0, 3.0)  # Before typing
    correction_probability: float = 0.05  # Chance of typo correction
    scroll_probability: float = 0.3  # Chance of scrolling before messaging
    read_time_per_word: Tuple[float, float] = (0.2, 0.4)  # Reading speed


class AntiDetectionService:
    """
    Service for implementing anti-detection features in Facebook messaging
    """
    
    def __init__(self):
        self.behavior_patterns = {
            BehaviorPattern.CONSERVATIVE: HumanBehaviorConfig(
                typing_speed_wpm=(20, 40),
                pause_between_words=(0.2, 0.5),
                pause_between_sentences=(1.0, 2.5),
                thinking_pause=(2.0, 5.0),
                correction_probability=0.08,
                scroll_probability=0.5,
                read_time_per_word=(0.3, 0.6)
            ),
            BehaviorPattern.NORMAL: HumanBehaviorConfig(),
            BehaviorPattern.ACTIVE: HumanBehaviorConfig(
                typing_speed_wpm=(50, 80),
                pause_between_words=(0.05, 0.2),
                pause_between_sentences=(0.3, 0.8),
                thinking_pause=(0.5, 2.0),
                correction_probability=0.03,
                scroll_probability=0.2,
                read_time_per_word=(0.1, 0.3)
            )
        }
        
        self.session_behaviors = {}  # profile_id -> current behavior
        self.session_stats = {}     # profile_id -> session statistics
    
    async def simulate_human_typing(
        self, 
        profile_id: int, 
        text: str, 
        pattern: BehaviorPattern = BehaviorPattern.NORMAL
    ) -> float:
        """Simulate human typing behavior and return total time taken"""
        try:
            config = self.behavior_patterns[pattern]
            total_time = 0.0
            
            # Thinking pause before typing
            thinking_time = random.uniform(*config.thinking_pause)
            await asyncio.sleep(thinking_time)
            total_time += thinking_time
            
            # Calculate typing time based on WPM
            words = text.split()
            wpm = random.uniform(*config.typing_speed_wpm)
            base_typing_time = len(words) / wpm * 60  # Convert to seconds
            
            # Add natural pauses
            for i, word in enumerate(words):
                # Time to type the word
                word_time = len(word) / (wpm * 5)  # Approximate characters per minute
                await asyncio.sleep(word_time)
                total_time += word_time
                
                # Pause between words
                if i < len(words) - 1:
                    word_pause = random.uniform(*config.pause_between_words)
                    await asyncio.sleep(word_pause)
                    total_time += word_pause
                
                # Longer pause at sentence endings
                if word.endswith(('.', '!', '?')):
                    sentence_pause = random.uniform(*config.pause_between_sentences)
                    await asyncio.sleep(sentence_pause)
                    total_time += sentence_pause
                
                # Simulate typo correction
                if random.random() < config.correction_probability:
                    correction_time = random.uniform(0.5, 2.0)
                    await asyncio.sleep(correction_time)
                    total_time += correction_time
            
            # Update session stats
            self._update_session_stats(profile_id, 'typing_time', total_time)
            
            logger.debug(f"Simulated typing for profile {profile_id}: {total_time:.2f}s for {len(words)} words")
            return total_time
            
        except Exception as e:
            logger.error(f"Failed to simulate typing for profile {profile_id}: {e}")
            return 0.0
    
    async def simulate_reading_time(
        self, 
        profile_id: int, 
        text: str, 
        pattern: BehaviorPattern = BehaviorPattern.NORMAL
    ) -> float:
        """Simulate time spent reading before responding"""
        try:
            config = self.behavior_patterns[pattern]
            
            words = text.split()
            read_time_per_word = random.uniform(*config.read_time_per_word)
            total_read_time = len(words) * read_time_per_word
            
            # Add some randomness
            total_read_time *= random.uniform(0.8, 1.2)
            
            await asyncio.sleep(total_read_time)
            
            self._update_session_stats(profile_id, 'reading_time', total_read_time)
            
            logger.debug(f"Simulated reading for profile {profile_id}: {total_read_time:.2f}s for {len(words)} words")
            return total_read_time
            
        except Exception as e:
            logger.error(f"Failed to simulate reading for profile {profile_id}: {e}")
            return 0.0
    
    async def simulate_page_interaction(
        self, 
        profile_id: int, 
        pattern: BehaviorPattern = BehaviorPattern.NORMAL
    ) -> float:
        """Simulate natural page interactions before messaging"""
        try:
            config = self.behavior_patterns[pattern]
            total_time = 0.0
            
            # Random scroll behavior
            if random.random() < config.scroll_probability:
                scroll_actions = random.randint(1, 3)
                for _ in range(scroll_actions):
                    scroll_time = random.uniform(0.5, 2.0)
                    await asyncio.sleep(scroll_time)
                    total_time += scroll_time
            
            # Random mouse movements (simulated with small delays)
            mouse_movements = random.randint(2, 5)
            for _ in range(mouse_movements):
                movement_time = random.uniform(0.1, 0.3)
                await asyncio.sleep(movement_time)
                total_time += movement_time
            
            # Pause to "look around"
            observation_time = random.uniform(1.0, 3.0)
            await asyncio.sleep(observation_time)
            total_time += observation_time
            
            self._update_session_stats(profile_id, 'interaction_time', total_time)
            
            logger.debug(f"Simulated page interaction for profile {profile_id}: {total_time:.2f}s")
            return total_time
            
        except Exception as e:
            logger.error(f"Failed to simulate page interaction for profile {profile_id}: {e}")
            return 0.0
    
    def get_adaptive_delay(
        self, 
        profile_id: int, 
        base_min: int, 
        base_max: int,
        pattern: BehaviorPattern = BehaviorPattern.NORMAL
    ) -> float:
        """Get adaptive delay based on session behavior and pattern"""
        try:
            # Get session stats
            stats = self.session_stats.get(profile_id, {})
            
            # Base delay
            delay = random.uniform(base_min, base_max)
            
            # Adjust based on pattern
            if pattern == BehaviorPattern.CONSERVATIVE:
                delay *= random.uniform(1.5, 2.5)
            elif pattern == BehaviorPattern.ACTIVE:
                delay *= random.uniform(0.5, 0.8)
            elif pattern == BehaviorPattern.RANDOM:
                delay *= random.uniform(0.3, 3.0)
            
            # Adjust based on recent activity
            recent_messages = stats.get('recent_message_count', 0)
            if recent_messages > 5:
                # Slow down if sending many messages
                delay *= random.uniform(1.2, 1.8)
            
            # Add time-of-day variation
            current_hour = datetime.now().hour
            if 22 <= current_hour or current_hour <= 6:
                # Night time - slower responses
                delay *= random.uniform(1.3, 2.0)
            elif 9 <= current_hour <= 17:
                # Work hours - faster responses
                delay *= random.uniform(0.8, 1.2)
            
            return max(delay, 1.0)  # Minimum 1 second delay
            
        except Exception as e:
            logger.error(f"Failed to calculate adaptive delay for profile {profile_id}: {e}")
            return random.uniform(base_min, base_max)
    
    def randomize_message_content(
        self, 
        template: str, 
        recipient_name: str = "",
        variation_level: float = 0.3
    ) -> str:
        """Add natural variations to message content"""
        try:
            content = template
            
            # Replace name placeholder
            if recipient_name:
                content = content.replace('{name}', recipient_name)
            
            if random.random() < variation_level:
                # Add greeting variations
                greetings = [
                    "Xin chào", "Chào bạn", "Hi", "Hello", 
                    "Chào", "Hey", "Xin chào bạn"
                ]
                
                # Add ending variations
                endings = [
                    "!", ".", " 😊", " 👋", " 🙂", 
                    " Thanks!", " Cảm ơn!", ""
                ]
                
                # Add casual words
                casual_words = [
                    "nhé", "nha", "ạ", "à", "thế", "vậy"
                ]
                
                # Apply random variations
                if not any(greeting in content.lower() for greeting in greetings):
                    if random.random() < 0.7:
                        content = f"{random.choice(greetings)} {content}"
                
                if random.random() < 0.5:
                    content += random.choice(endings)
                
                if random.random() < 0.3:
                    content += f" {random.choice(casual_words)}"
            
            return content
            
        except Exception as e:
            logger.error(f"Failed to randomize message content: {e}")
            return template
    
    def get_behavior_pattern_for_profile(self, profile_id: int) -> BehaviorPattern:
        """Get or assign behavior pattern for a profile"""
        if profile_id not in self.session_behaviors:
            # Assign random pattern with weights
            patterns = [
                (BehaviorPattern.CONSERVATIVE, 0.2),
                (BehaviorPattern.NORMAL, 0.6),
                (BehaviorPattern.ACTIVE, 0.15),
                (BehaviorPattern.RANDOM, 0.05)
            ]
            
            rand = random.random()
            cumulative = 0.0
            
            for pattern, weight in patterns:
                cumulative += weight
                if rand <= cumulative:
                    self.session_behaviors[profile_id] = pattern
                    break
            else:
                self.session_behaviors[profile_id] = BehaviorPattern.NORMAL
        
        return self.session_behaviors[profile_id]
    
    def _update_session_stats(self, profile_id: int, stat_name: str, value: float):
        """Update session statistics for a profile"""
        if profile_id not in self.session_stats:
            self.session_stats[profile_id] = {
                'session_start': datetime.now(),
                'total_typing_time': 0.0,
                'total_reading_time': 0.0,
                'total_interaction_time': 0.0,
                'message_count': 0,
                'recent_message_count': 0,
                'last_message_time': None
            }
        
        stats = self.session_stats[profile_id]
        
        if stat_name == 'typing_time':
            stats['total_typing_time'] += value
        elif stat_name == 'reading_time':
            stats['total_reading_time'] += value
        elif stat_name == 'interaction_time':
            stats['total_interaction_time'] += value
        elif stat_name == 'message_sent':
            stats['message_count'] += 1
            stats['recent_message_count'] += 1
            stats['last_message_time'] = datetime.now()
            
            # Reset recent count every hour
            if (stats['last_message_time'] and 
                datetime.now() - stats['last_message_time'] > timedelta(hours=1)):
                stats['recent_message_count'] = 1
    
    def get_session_stats(self, profile_id: int) -> Dict[str, Any]:
        """Get session statistics for a profile"""
        return self.session_stats.get(profile_id, {})
    
    def reset_session_stats(self, profile_id: int):
        """Reset session statistics for a profile"""
        if profile_id in self.session_stats:
            del self.session_stats[profile_id]
        if profile_id in self.session_behaviors:
            del self.session_behaviors[profile_id]
    
    def get_fingerprint_rotation_config(self) -> Dict[str, Any]:
        """Get configuration for fingerprint rotation"""
        return {
            'rotate_after_messages': random.randint(50, 150),
            'rotate_after_hours': random.randint(6, 24),
            'user_agent_pool_size': 10,
            'viewport_variations': 5,
            'timezone_rotation': True
        }


# Global instance
anti_detection_service = AntiDetectionService()
