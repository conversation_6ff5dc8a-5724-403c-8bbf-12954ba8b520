"""
Performance Optimizer for Facebook Scraping
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque
import redis.asyncio as redis

from ..core.config import settings
from ..core.logger import setup_logger

logger = setup_logger(__name__)


class ConnectionPool:
    """Manages browser connection pooling"""
    
    def __init__(self, max_connections: int = 5):
        self.max_connections = max_connections
        self.active_connections = {}
        self.available_connections = deque()
        self.connection_stats = defaultdict(int)
        self._lock = asyncio.Lock()
    
    async def get_connection(self, profile_id: int):
        """Get an available connection or create new one"""
        async with self._lock:
            connection_key = f"profile_{profile_id}"
            
            # Check if we have an active connection for this profile
            if connection_key in self.active_connections:
                connection = self.active_connections[connection_key]
                if await self._is_connection_healthy(connection):
                    self.connection_stats['reused'] += 1
                    return connection
                else:
                    # Remove unhealthy connection
                    await self._cleanup_connection(connection_key)
            
            # Try to get from available pool
            if self.available_connections:
                connection = self.available_connections.popleft()
                if await self._is_connection_healthy(connection):
                    self.active_connections[connection_key] = connection
                    self.connection_stats['reused'] += 1
                    return connection
            
            # Create new connection if under limit
            if len(self.active_connections) < self.max_connections:
                connection = await self._create_new_connection(profile_id)
                if connection:
                    self.active_connections[connection_key] = connection
                    self.connection_stats['created'] += 1
                    return connection
            
            # Wait for available connection
            return await self._wait_for_available_connection(profile_id)
    
    async def release_connection(self, profile_id: int, connection):
        """Release a connection back to the pool"""
        async with self._lock:
            connection_key = f"profile_{profile_id}"
            
            if connection_key in self.active_connections:
                del self.active_connections[connection_key]
            
            # Add to available pool if healthy and under limit
            if (await self._is_connection_healthy(connection) and 
                len(self.available_connections) < self.max_connections):
                self.available_connections.append(connection)
            else:
                await self._cleanup_connection_object(connection)
    
    async def _create_new_connection(self, profile_id: int):
        """Create a new browser connection"""
        try:
            from .facebook_scraper import FacebookScraper
            scraper = FacebookScraper()
            
            # Create a temporary task ID for connection
            temp_task_id = f"pool_{profile_id}_{int(time.time())}"
            connection = await scraper.create_scraping_session(profile_id, temp_task_id)
            
            return connection
            
        except Exception as e:
            logger.error(f"Failed to create new connection for profile {profile_id}: {e}")
            return None
    
    async def _is_connection_healthy(self, connection) -> bool:
        """Check if connection is still healthy"""
        try:
            # Simple health check - try to get current URL
            if hasattr(connection, 'browser_manager') and connection.browser_manager:
                return True
            return False
        except:
            return False
    
    async def _cleanup_connection(self, connection_key: str):
        """Clean up a specific connection"""
        if connection_key in self.active_connections:
            connection = self.active_connections[connection_key]
            await self._cleanup_connection_object(connection)
            del self.active_connections[connection_key]
    
    async def _cleanup_connection_object(self, connection):
        """Clean up connection object"""
        try:
            if hasattr(connection, 'close'):
                await connection.close()
        except Exception as e:
            logger.warning(f"Error cleaning up connection: {e}")
    
    async def _wait_for_available_connection(self, profile_id: int, timeout: int = 30):
        """Wait for an available connection"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            await asyncio.sleep(1)
            
            # Check if any connection became available
            async with self._lock:
                if self.available_connections:
                    connection = self.available_connections.popleft()
                    if await self._is_connection_healthy(connection):
                        connection_key = f"profile_{profile_id}"
                        self.active_connections[connection_key] = connection
                        return connection
        
        raise Exception(f"Timeout waiting for available connection for profile {profile_id}")
    
    async def cleanup_all(self):
        """Clean up all connections"""
        async with self._lock:
            # Clean up active connections
            for connection in self.active_connections.values():
                await self._cleanup_connection_object(connection)
            self.active_connections.clear()
            
            # Clean up available connections
            while self.available_connections:
                connection = self.available_connections.popleft()
                await self._cleanup_connection_object(connection)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        return {
            'active_connections': len(self.active_connections),
            'available_connections': len(self.available_connections),
            'max_connections': self.max_connections,
            'stats': dict(self.connection_stats)
        }


class CacheManager:
    """Manages caching for scraped data"""
    
    def __init__(self):
        self.redis_client = None
        self.local_cache = {}
        self.cache_ttl = 3600  # 1 hour
        self._init_redis()
    
    def _init_redis(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.from_url(settings.REDIS_URL)
        except Exception as e:
            logger.warning(f"Failed to connect to Redis: {e}. Using local cache only.")
    
    async def get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache"""
        try:
            # Try Redis first
            if self.redis_client:
                data = await self.redis_client.get(cache_key)
                if data:
                    return json.loads(data)
            
            # Fall back to local cache
            if cache_key in self.local_cache:
                cached_item = self.local_cache[cache_key]
                if cached_item['expires_at'] > datetime.now():
                    return cached_item['data']
                else:
                    del self.local_cache[cache_key]
            
            return None
            
        except Exception as e:
            logger.warning(f"Error getting cached data for {cache_key}: {e}")
            return None
    
    async def set_cached_data(self, cache_key: str, data: Any, ttl: Optional[int] = None):
        """Set data in cache"""
        try:
            ttl = ttl or self.cache_ttl
            
            # Try Redis first
            if self.redis_client:
                await self.redis_client.setex(
                    cache_key, 
                    ttl, 
                    json.dumps(data, default=str)
                )
            
            # Also store in local cache
            self.local_cache[cache_key] = {
                'data': data,
                'expires_at': datetime.now() + timedelta(seconds=ttl)
            }
            
        except Exception as e:
            logger.warning(f"Error setting cached data for {cache_key}: {e}")
    
    async def invalidate_cache(self, pattern: str):
        """Invalidate cache entries matching pattern"""
        try:
            # Redis invalidation
            if self.redis_client:
                keys = await self.redis_client.keys(pattern)
                if keys:
                    await self.redis_client.delete(*keys)
            
            # Local cache invalidation
            keys_to_remove = [key for key in self.local_cache.keys() if pattern in key]
            for key in keys_to_remove:
                del self.local_cache[key]
                
        except Exception as e:
            logger.warning(f"Error invalidating cache for pattern {pattern}: {e}")
    
    def cleanup_expired(self):
        """Clean up expired local cache entries"""
        now = datetime.now()
        expired_keys = [
            key for key, item in self.local_cache.items()
            if item['expires_at'] <= now
        ]
        
        for key in expired_keys:
            del self.local_cache[key]


class BatchProcessor:
    """Handles batch processing of scraping tasks"""
    
    def __init__(self, batch_size: int = 10, max_concurrent: int = 3):
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.processing_queue = asyncio.Queue()
        self.results_queue = asyncio.Queue()
        self.active_batches = {}
        self._workers_started = False
    
    async def start_workers(self):
        """Start batch processing workers"""
        if self._workers_started:
            return
        
        for i in range(self.max_concurrent):
            asyncio.create_task(self._batch_worker(f"worker_{i}"))
        
        self._workers_started = True
        logger.info(f"Started {self.max_concurrent} batch processing workers")
    
    async def _batch_worker(self, worker_id: str):
        """Batch processing worker"""
        while True:
            try:
                batch = await self.processing_queue.get()
                
                if batch is None:  # Shutdown signal
                    break
                
                batch_id = batch['id']
                items = batch['items']
                processor_func = batch['processor']
                
                logger.info(f"Worker {worker_id} processing batch {batch_id} with {len(items)} items")
                
                # Process batch
                results = []
                for item in items:
                    try:
                        result = await processor_func(item)
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Error processing item in batch {batch_id}: {e}")
                        results.append({'error': str(e), 'item': item})
                
                # Store results
                await self.results_queue.put({
                    'batch_id': batch_id,
                    'results': results,
                    'worker_id': worker_id
                })
                
                self.processing_queue.task_done()
                
            except Exception as e:
                logger.error(f"Batch worker {worker_id} error: {e}")
                await asyncio.sleep(1)
    
    async def submit_batch(self, items: List[Any], processor_func, batch_id: str = None) -> str:
        """Submit items for batch processing"""
        if not self._workers_started:
            await self.start_workers()
        
        batch_id = batch_id or f"batch_{int(time.time())}_{len(items)}"
        
        # Split into smaller batches if needed
        for i in range(0, len(items), self.batch_size):
            sub_batch_items = items[i:i + self.batch_size]
            sub_batch_id = f"{batch_id}_sub_{i // self.batch_size}"
            
            batch = {
                'id': sub_batch_id,
                'items': sub_batch_items,
                'processor': processor_func
            }
            
            await self.processing_queue.put(batch)
            self.active_batches[sub_batch_id] = {
                'submitted_at': datetime.now(),
                'item_count': len(sub_batch_items)
            }
        
        return batch_id
    
    async def get_batch_results(self, timeout: int = 60) -> Optional[Dict[str, Any]]:
        """Get batch processing results"""
        try:
            result = await asyncio.wait_for(
                self.results_queue.get(),
                timeout=timeout
            )
            
            batch_id = result['batch_id']
            if batch_id in self.active_batches:
                del self.active_batches[batch_id]
            
            return result
            
        except asyncio.TimeoutError:
            logger.warning("Timeout waiting for batch results")
            return None
    
    async def shutdown(self):
        """Shutdown batch processor"""
        # Send shutdown signals to workers
        for _ in range(self.max_concurrent):
            await self.processing_queue.put(None)
        
        # Wait for workers to finish
        await self.processing_queue.join()
        
        self._workers_started = False
        logger.info("Batch processor shutdown complete")


class PerformanceOptimizer:
    """Main performance optimizer class"""
    
    def __init__(self):
        self.connection_pool = ConnectionPool(max_connections=settings.MAX_CONCURRENT_BROWSERS)
        self.cache_manager = CacheManager()
        self.batch_processor = BatchProcessor(
            batch_size=10,
            max_concurrent=settings.MAX_SCRAPING_WORKERS
        )
        self.performance_metrics = defaultdict(list)
    
    async def optimize_scraping_session(self, profile_id: int, task_id: str):
        """Get optimized scraping session"""
        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = f"session_{profile_id}"
            cached_session = await self.cache_manager.get_cached_data(cache_key)
            
            if cached_session:
                logger.info(f"Using cached session for profile {profile_id}")
                return cached_session
            
            # Get connection from pool
            connection = await self.connection_pool.get_connection(profile_id)
            
            # Cache the session
            await self.cache_manager.set_cached_data(cache_key, connection, ttl=1800)
            
            # Record metrics
            duration = time.time() - start_time
            self.performance_metrics['session_creation'].append(duration)
            
            return connection
            
        except Exception as e:
            logger.error(f"Failed to optimize scraping session: {e}")
            raise
    
    async def batch_process_urls(self, urls: List[str], processor_func) -> List[Any]:
        """Process multiple URLs in batches"""
        batch_id = await self.batch_processor.submit_batch(urls, processor_func)
        
        results = []
        processed_batches = 0
        expected_batches = (len(urls) + self.batch_processor.batch_size - 1) // self.batch_processor.batch_size
        
        while processed_batches < expected_batches:
            batch_result = await self.batch_processor.get_batch_results()
            if batch_result:
                results.extend(batch_result['results'])
                processed_batches += 1
            else:
                break
        
        return results
    
    async def cleanup(self):
        """Clean up optimizer resources"""
        await self.connection_pool.cleanup_all()
        await self.batch_processor.shutdown()
        self.cache_manager.cleanup_expired()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            'connection_pool': self.connection_pool.get_stats(),
            'cache_stats': {
                'local_cache_size': len(self.cache_manager.local_cache)
            },
            'batch_processor': {
                'active_batches': len(self.batch_processor.active_batches),
                'queue_size': self.batch_processor.processing_queue.qsize()
            },
            'performance_metrics': {
                metric: {
                    'count': len(values),
                    'avg': sum(values) / len(values) if values else 0,
                    'min': min(values) if values else 0,
                    'max': max(values) if values else 0
                }
                for metric, values in self.performance_metrics.items()
            }
        }


# Global instance
performance_optimizer = PerformanceOptimizer()
