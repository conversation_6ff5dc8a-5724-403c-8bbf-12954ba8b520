"""
Facebook Scraper Service - Enhanced AsyncWebCrawler for Facebook
"""

import asyncio
import json
import random
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from crawl4ai.cache_context import CacheMode

from ..core.config import settings
from ..core.logger import setup_logger
from ..models.scraping import ScrapingType, ScrapingTaskStatus
from .profile_manager import AntidetectProfileManager

logger = setup_logger(__name__)


class FacebookScraper:
    """
    Enhanced Facebook scraper using crawl4ai with anti-detection
    """
    
    def __init__(self):
        self.profile_manager = AntidetectProfileManager()
        self.active_crawlers = {}  # task_id -> crawler_instance
        self.extraction_strategies = self._create_extraction_strategies()
        
    def _create_extraction_strategies(self) -> Dict[str, JsonCssExtractionStrategy]:
        """Create extraction strategies for different Facebook elements"""
        
        strategies = {}
        
        # Comments extraction strategy
        strategies['comments'] = JsonCssExtractionStrategy({
            "name": "Facebook Comments",
            "baseSelector": "[data-testid='UFI2Comment/root'], .x1y1aw1k.xn6708d.xwib8y2.x1ye3gou",
            "fields": [
                {
                    "name": "user_id",
                    "selector": "[data-testid='UFI2Comment/link'] a, .x1i10hfl a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "user_name",
                    "selector": "[data-testid='UFI2Comment/link'] a, .x1i10hfl a",
                    "type": "text"
                },
                {
                    "name": "comment_text",
                    "selector": "[data-testid='UFI2Comment/body'] span, .x193iq5w span",
                    "type": "text"
                },
                {
                    "name": "comment_time",
                    "selector": "[data-testid='UFI2Comment/timestamp'] a, .x1i10hfl[role='link']",
                    "type": "attribute",
                    "attribute": "aria-label"
                },
                {
                    "name": "profile_picture",
                    "selector": "img[data-testid='UFI2Comment/profilePhoto'], .x1ey2m1c img",
                    "type": "attribute",
                    "attribute": "src"
                }
            ]
        })
        
        # Likes extraction strategy
        strategies['likes'] = JsonCssExtractionStrategy({
            "name": "Facebook Likes",
            "baseSelector": "[data-testid='UFI2ReactionsMenu/root'] [role='button'], .x1i10hfl[data-testid*='reaction']",
            "fields": [
                {
                    "name": "user_id",
                    "selector": "a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "user_name",
                    "selector": "a",
                    "type": "text"
                },
                {
                    "name": "reaction_type",
                    "selector": "[data-testid*='reaction']",
                    "type": "attribute",
                    "attribute": "data-testid"
                }
            ]
        })
        
        # Shares extraction strategy
        strategies['shares'] = JsonCssExtractionStrategy({
            "name": "Facebook Shares",
            "baseSelector": "[data-testid='UFI2SharesCount/root'], .x1i10hfl[data-testid*='share']",
            "fields": [
                {
                    "name": "user_id",
                    "selector": "a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "user_name",
                    "selector": "a",
                    "type": "text"
                },
                {
                    "name": "share_type",
                    "selector": "[data-testid*='share']",
                    "type": "attribute",
                    "attribute": "data-testid"
                }
            ]
        })
        
        # Post info extraction
        strategies['post_info'] = JsonCssExtractionStrategy({
            "name": "Facebook Post Info",
            "baseSelector": "[data-pagelet='FeedUnit'], [data-testid='story-subtitle']",
            "fields": [
                {
                    "name": "post_id",
                    "selector": "[data-ft]",
                    "type": "attribute",
                    "attribute": "data-ft"
                },
                {
                    "name": "author_name",
                    "selector": "[data-testid='story-subtitle'] a, .x1i10hfl strong",
                    "type": "text"
                },
                {
                    "name": "author_id",
                    "selector": "[data-testid='story-subtitle'] a, .x1i10hfl",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "post_text",
                    "selector": "[data-testid='post_message'], .x11i5rnm.xat24cr.x1mh8g0r.x1vvkbs",
                    "type": "text"
                },
                {
                    "name": "post_time",
                    "selector": "[data-testid='story-subtitle'] a[role='link'], .x1i10hfl[role='link']",
                    "type": "attribute",
                    "attribute": "aria-label"
                }
            ]
        })
        
        return strategies
    
    async def create_scraping_session(self, profile_id: int, task_id: str) -> Optional[AsyncWebCrawler]:
        """Create a scraping session with the specified profile"""
        try:
            # Try to get optimized session first
            from .performance_optimizer import performance_optimizer

            try:
                optimized_session = await performance_optimizer.optimize_scraping_session(profile_id, task_id)
                if optimized_session:
                    return optimized_session
            except Exception as e:
                logger.warning(f"Failed to get optimized session, creating new one: {e}")

            # Get profile from database (you'll need to implement this)
            # For now, we'll use a mock profile
            profile_path = settings.PROFILES_DIR / f"profile_{profile_id}"
            
            # Load profile metadata
            metadata_file = profile_path / 'metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                fingerprint = metadata.get('fingerprint', {})
                proxy_config = metadata.get('proxy_config', {})
            else:
                # Generate new fingerprint if metadata doesn't exist
                from .fingerprint_generator import FingerprintGenerator
                fingerprint_gen = FingerprintGenerator()
                fingerprint = await fingerprint_gen.generate_fingerprint()
                proxy_config = {}
            
            # Create browser config
            browser_config = BrowserConfig(
                browser_type='chromium',
                headless=False,  # Facebook detection is harder with headless
                user_agent=fingerprint.get('user_agent'),
                viewport_width=fingerprint.get('viewport', {}).get('width', 1366),
                viewport_height=fingerprint.get('viewport', {}).get('height', 768),
                use_managed_browser=True,
                user_data_dir=str(profile_path),
                extra_args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-field-trial-config',
                    '--disable-ipc-flooding-protection'
                ]
            )
            
            # Add proxy if configured
            if proxy_config.get('type') != 'no_proxy' and proxy_config.get('host'):
                proxy_url = self._build_proxy_url(proxy_config)
                if proxy_url:
                    browser_config.proxy_config = {
                        'server': proxy_url,
                        'username': proxy_config.get('username'),
                        'password': proxy_config.get('password')
                    }
            
            # Create crawler
            crawler = AsyncWebCrawler(config=browser_config)
            await crawler.start()
            
            # Store crawler reference
            self.active_crawlers[task_id] = crawler
            
            logger.info(f"Created scraping session for profile {profile_id}, task {task_id}")
            return crawler
            
        except Exception as e:
            logger.error(f"Failed to create scraping session: {e}")
            return None
    
    def _build_proxy_url(self, proxy_config: Dict) -> Optional[str]:
        """Build proxy URL from configuration"""
        proxy_type = proxy_config.get('type')
        host = proxy_config.get('host')
        port = proxy_config.get('port')
        
        if not host or not port:
            return None
            
        if proxy_type == 'http':
            return f"http://{host}:{port}"
        elif proxy_type == 'https':
            return f"https://{host}:{port}"
        elif proxy_type == 'socks5':
            return f"socks5://{host}:{port}"
        
        return None
    
    async def scrape_facebook_post(
        self, 
        crawler: AsyncWebCrawler,
        post_url: str,
        scraping_types: List[ScrapingType],
        max_results: int = 1000
    ) -> Dict[str, Any]:
        """Scrape a Facebook post for interactions"""
        try:
            results = {
                'post_info': {},
                'comments': [],
                'likes': [],
                'shares': [],
                'total_found': 0,
                'errors': []
            }
            
            # Navigate to post
            logger.info(f"Navigating to Facebook post: {post_url}")
            
            crawler_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                wait_for="css:[data-pagelet='FeedUnit'], [data-testid='story-subtitle']",
                page_timeout=30000,
                delay_before_return_html=3000  # Wait for dynamic content
            )
            
            result = await crawler.arun(post_url, config=crawler_config)
            
            if not result.success:
                raise Exception(f"Failed to load post: {result.error_message}")
            
            # Extract post information
            if ScrapingType.ALL in scraping_types or any(t in scraping_types for t in [ScrapingType.COMMENTS, ScrapingType.LIKES, ScrapingType.SHARES]):
                post_info_result = await crawler.arun(
                    post_url,
                    config=CrawlerRunConfig(
                        extraction_strategy=self.extraction_strategies['post_info'],
                        cache_mode=CacheMode.BYPASS
                    )
                )
                
                if post_info_result.success and post_info_result.extracted_content:
                    try:
                        post_data = json.loads(post_info_result.extracted_content)
                        if post_data and len(post_data) > 0:
                            results['post_info'] = post_data[0]
                    except json.JSONDecodeError:
                        logger.warning("Failed to parse post info JSON")
            
            # Scrape comments
            if ScrapingType.COMMENTS in scraping_types or ScrapingType.ALL in scraping_types:
                comments = await self._scrape_comments(crawler, post_url, max_results)
                results['comments'] = comments
                results['total_found'] += len(comments)
            
            # Scrape likes
            if ScrapingType.LIKES in scraping_types or ScrapingType.ALL in scraping_types:
                likes = await self._scrape_likes(crawler, post_url, max_results)
                results['likes'] = likes
                results['total_found'] += len(likes)
            
            # Scrape shares
            if ScrapingType.SHARES in scraping_types or ScrapingType.ALL in scraping_types:
                shares = await self._scrape_shares(crawler, post_url, max_results)
                results['shares'] = shares
                results['total_found'] += len(shares)
            
            logger.info(f"Scraping completed. Total found: {results['total_found']}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to scrape Facebook post: {e}")
            return {
                'post_info': {},
                'comments': [],
                'likes': [],
                'shares': [],
                'total_found': 0,
                'errors': [str(e)]
            }
    
    async def _scrape_comments(self, crawler: AsyncWebCrawler, post_url: str, max_results: int) -> List[Dict]:
        """Scrape comments from a Facebook post"""
        try:
            # First, try to load more comments by scrolling
            await self._load_more_content(crawler, "comments", max_results)
            
            # Extract comments
            result = await crawler.arun(
                post_url,
                config=CrawlerRunConfig(
                    extraction_strategy=self.extraction_strategies['comments'],
                    cache_mode=CacheMode.BYPASS
                )
            )
            
            if result.success and result.extracted_content:
                try:
                    comments_data = json.loads(result.extracted_content)
                    return self._process_comments_data(comments_data)
                except json.JSONDecodeError:
                    logger.warning("Failed to parse comments JSON")
            
            return []
            
        except Exception as e:
            logger.error(f"Failed to scrape comments: {e}")
            return []
    
    async def _scrape_likes(self, crawler: AsyncWebCrawler, post_url: str, max_results: int) -> List[Dict]:
        """Scrape likes from a Facebook post"""
        try:
            # Click on likes count to open likes modal
            # This is a simplified version - actual implementation would need more sophisticated interaction
            
            result = await crawler.arun(
                post_url,
                config=CrawlerRunConfig(
                    extraction_strategy=self.extraction_strategies['likes'],
                    cache_mode=CacheMode.BYPASS
                )
            )
            
            if result.success and result.extracted_content:
                try:
                    likes_data = json.loads(result.extracted_content)
                    return self._process_likes_data(likes_data)
                except json.JSONDecodeError:
                    logger.warning("Failed to parse likes JSON")
            
            return []
            
        except Exception as e:
            logger.error(f"Failed to scrape likes: {e}")
            return []
    
    async def _scrape_shares(self, crawler: AsyncWebCrawler, post_url: str, max_results: int) -> List[Dict]:
        """Scrape shares from a Facebook post"""
        try:
            result = await crawler.arun(
                post_url,
                config=CrawlerRunConfig(
                    extraction_strategy=self.extraction_strategies['shares'],
                    cache_mode=CacheMode.BYPASS
                )
            )
            
            if result.success and result.extracted_content:
                try:
                    shares_data = json.loads(result.extracted_content)
                    return self._process_shares_data(shares_data)
                except json.JSONDecodeError:
                    logger.warning("Failed to parse shares JSON")
            
            return []
            
        except Exception as e:
            logger.error(f"Failed to scrape shares: {e}")
            return []
    
    async def _load_more_content(self, crawler: AsyncWebCrawler, content_type: str, max_results: int):
        """Load more content by scrolling and clicking 'Load More' buttons"""
        try:
            # This would implement scrolling and clicking load more buttons
            # For now, it's a placeholder
            await asyncio.sleep(random.uniform(2, 4))  # Random delay
            
        except Exception as e:
            logger.warning(f"Failed to load more {content_type}: {e}")
    
    def _process_comments_data(self, comments_data: List[Dict]) -> List[Dict]:
        """Process and clean comments data"""
        processed = []
        
        for comment in comments_data:
            try:
                user_id = self._extract_user_id_from_url(comment.get('user_id', ''))
                
                processed_comment = {
                    'facebook_uid': user_id,
                    'full_name': comment.get('user_name', '').strip(),
                    'profile_url': comment.get('user_id', ''),
                    'interaction_type': 'comment',
                    'interaction_content': comment.get('comment_text', '').strip(),
                    'interaction_timestamp': self._parse_facebook_time(comment.get('comment_time', '')),
                    'profile_picture_url': comment.get('profile_picture', ''),
                    'metadata': {
                        'raw_data': comment
                    }
                }
                
                if user_id:  # Only add if we have a valid user ID
                    processed.append(processed_comment)
                    
            except Exception as e:
                logger.warning(f"Failed to process comment: {e}")
                continue
        
        return processed
    
    def _process_likes_data(self, likes_data: List[Dict]) -> List[Dict]:
        """Process and clean likes data"""
        processed = []
        
        for like in likes_data:
            try:
                user_id = self._extract_user_id_from_url(like.get('user_id', ''))
                
                processed_like = {
                    'facebook_uid': user_id,
                    'full_name': like.get('user_name', '').strip(),
                    'profile_url': like.get('user_id', ''),
                    'interaction_type': 'like',
                    'interaction_content': like.get('reaction_type', 'like'),
                    'interaction_timestamp': datetime.now(),
                    'metadata': {
                        'reaction_type': like.get('reaction_type', 'like'),
                        'raw_data': like
                    }
                }
                
                if user_id:
                    processed.append(processed_like)
                    
            except Exception as e:
                logger.warning(f"Failed to process like: {e}")
                continue
        
        return processed
    
    def _process_shares_data(self, shares_data: List[Dict]) -> List[Dict]:
        """Process and clean shares data"""
        processed = []
        
        for share in shares_data:
            try:
                user_id = self._extract_user_id_from_url(share.get('user_id', ''))
                
                processed_share = {
                    'facebook_uid': user_id,
                    'full_name': share.get('user_name', '').strip(),
                    'profile_url': share.get('user_id', ''),
                    'interaction_type': 'share',
                    'interaction_content': share.get('share_type', 'share'),
                    'interaction_timestamp': datetime.now(),
                    'metadata': {
                        'share_type': share.get('share_type', 'share'),
                        'raw_data': share
                    }
                }
                
                if user_id:
                    processed.append(processed_share)
                    
            except Exception as e:
                logger.warning(f"Failed to process share: {e}")
                continue
        
        return processed
    
    def _extract_user_id_from_url(self, url: str) -> str:
        """Extract Facebook user ID from profile URL"""
        if not url:
            return ""
        
        try:
            # Handle different Facebook URL formats
            if '/profile.php?id=' in url:
                # Format: https://www.facebook.com/profile.php?id=100001234567890
                return url.split('id=')[1].split('&')[0]
            elif '/people/' in url:
                # Format: https://www.facebook.com/people/Name/100001234567890
                parts = url.split('/people/')
                if len(parts) > 1:
                    return parts[1].split('/')[1] if '/' in parts[1] else parts[1]
            else:
                # Format: https://www.facebook.com/username
                parts = url.split('facebook.com/')
                if len(parts) > 1:
                    username = parts[1].split('/')[0].split('?')[0]
                    return username
            
            return ""
            
        except Exception as e:
            logger.warning(f"Failed to extract user ID from URL {url}: {e}")
            return ""
    
    def _parse_facebook_time(self, time_str: str) -> Optional[datetime]:
        """Parse Facebook timestamp string"""
        try:
            # This would implement Facebook time parsing
            # For now, return current time
            return datetime.now()
        except Exception:
            return None
    
    async def close_scraping_session(self, task_id: str):
        """Close a scraping session"""
        try:
            if task_id in self.active_crawlers:
                crawler = self.active_crawlers[task_id]
                await crawler.close()
                del self.active_crawlers[task_id]
                logger.info(f"Closed scraping session for task {task_id}")
        except Exception as e:
            logger.error(f"Failed to close scraping session {task_id}: {e}")
    
    async def cleanup_all_sessions(self):
        """Close all active scraping sessions"""
        for task_id in list(self.active_crawlers.keys()):
            await self.close_scraping_session(task_id)
