"""
Profile Manager Service - Enhanced version of crawl4ai BrowserProfiler
"""

import os
import json
import uuid
import asyncio
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime

from crawl4ai.browser_profiler import BrowserProfiler
from crawl4ai.async_configs import BrowserConfig
from crawl4ai import AsyncWebCrawler

from ..core.config import settings
from ..core.logger import setup_logger
from ..models.profile import Profile, ProxyType, ProfileStatus, ProxyConfig
from .fingerprint_generator import FingerprintGenerator

logger = setup_logger(__name__)


class AntidetectProfileManager:
    """
    Enhanced Profile Manager with antidetect capabilities
    Built on top of crawl4ai's BrowserProfiler
    """
    
    def __init__(self):
        self.profiler = BrowserProfiler()
        self.fingerprint_generator = FingerprintGenerator()
        self.active_browsers = {}  # profile_id -> browser_instance
        
    async def create_profile(self, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new antidetect browser profile
        
        Args:
            profile_data: Profile configuration data
            
        Returns:
            Dict containing profile creation result
        """
        try:
            profile_name = profile_data['name']
            proxy_config = profile_data.get('proxy_config', {})
            
            logger.info(f"Creating profile: {profile_name}")
            
            # Generate unique profile path
            profile_path = settings.PROFILES_DIR / f"{profile_name}_{uuid.uuid4().hex[:8]}"
            profile_path.mkdir(exist_ok=True)
            
            # Generate browser fingerprint
            fingerprint = await self.fingerprint_generator.generate_fingerprint()
            
            # Create browser config with proxy if specified
            browser_config = self._create_browser_config(proxy_config, fingerprint)
            
            # Use crawl4ai's BrowserProfiler to create the profile
            actual_profile_path = await self.profiler.create_profile(
                profile_name=profile_name,
                browser_config=browser_config
            )
            
            # Save fingerprint and metadata
            metadata = {
                'fingerprint': fingerprint,
                'proxy_config': proxy_config,
                'created_at': datetime.now().isoformat(),
                'user_agent': fingerprint.get('user_agent'),
                'viewport': fingerprint.get('viewport')
            }
            
            metadata_file = Path(actual_profile_path) / 'metadata.json'
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"Profile created successfully: {actual_profile_path}")
            
            return {
                'success': True,
                'profile_path': str(actual_profile_path),
                'fingerprint': fingerprint,
                'message': f'Profile {profile_name} created successfully'
            }
            
        except Exception as e:
            logger.error(f"Failed to create profile {profile_name}: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'Failed to create profile: {e}'
            }
    
    def _create_browser_config(self, proxy_config: Dict, fingerprint: Dict) -> BrowserConfig:
        """Create browser config with proxy and fingerprint settings"""
        
        config_params = {
            'browser_type': 'chromium',
            'headless': False,
            'user_agent': fingerprint.get('user_agent'),
            'viewport_width': fingerprint.get('viewport', {}).get('width', 1366),
            'viewport_height': fingerprint.get('viewport', {}).get('height', 768),
            'extra_args': [
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        }
        
        # Add proxy configuration if specified
        if proxy_config.get('type') != ProxyType.NO_PROXY and proxy_config.get('host'):
            proxy_url = self._build_proxy_url(proxy_config)
            if proxy_url:
                config_params['proxy_config'] = {
                    'server': proxy_url,
                    'username': proxy_config.get('username'),
                    'password': proxy_config.get('password')
                }
        
        return BrowserConfig(**config_params)
    
    def _build_proxy_url(self, proxy_config: Dict) -> Optional[str]:
        """Build proxy URL from configuration"""
        proxy_type = proxy_config.get('type')
        host = proxy_config.get('host')
        port = proxy_config.get('port')
        
        if not host or not port:
            return None
            
        if proxy_type == ProxyType.HTTP:
            return f"http://{host}:{port}"
        elif proxy_type == ProxyType.HTTPS:
            return f"https://{host}:{port}"
        elif proxy_type == ProxyType.SOCKS5:
            return f"socks5://{host}:{port}"
        
        return None
    
    async def test_profile(self, profile_path: str, proxy_config: Dict) -> Dict[str, Any]:
        """
        Test profile connectivity and proxy
        
        Args:
            profile_path: Path to the profile directory
            proxy_config: Proxy configuration
            
        Returns:
            Dict containing test results
        """
        try:
            logger.info(f"Testing profile: {profile_path}")
            
            # Load profile metadata
            metadata_file = Path(profile_path) / 'metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                fingerprint = metadata.get('fingerprint', {})
            else:
                fingerprint = await self.fingerprint_generator.generate_fingerprint()
            
            # Create browser config
            browser_config = self._create_browser_config(proxy_config, fingerprint)
            browser_config.user_data_dir = profile_path
            browser_config.use_managed_browser = True
            
            # Test with a simple page
            async with AsyncWebCrawler(config=browser_config) as crawler:
                # Test basic connectivity
                result = await crawler.arun("https://httpbin.org/ip")
                
                if result.success:
                    try:
                        ip_data = json.loads(result.html)
                        ip_address = ip_data.get('origin', 'Unknown')
                    except:
                        ip_address = 'Unknown'
                    
                    return {
                        'success': True,
                        'message': 'Profile test successful',
                        'ip_address': ip_address,
                        'user_agent': fingerprint.get('user_agent'),
                        'fingerprint': fingerprint
                    }
                else:
                    return {
                        'success': False,
                        'message': f'Profile test failed: {result.error_message}',
                        'error': result.error_message
                    }
                    
        except Exception as e:
            logger.error(f"Profile test failed: {e}")
            return {
                'success': False,
                'message': f'Profile test failed: {e}',
                'error': str(e)
            }
    
    async def login_facebook(self, profile_path: str, credentials: Dict[str, str]) -> Dict[str, Any]:
        """
        Login to Facebook using the profile
        
        Args:
            profile_path: Path to the profile directory
            credentials: Facebook login credentials
            
        Returns:
            Dict containing login result
        """
        try:
            logger.info(f"Attempting Facebook login for profile: {profile_path}")
            
            # Load profile metadata
            metadata_file = Path(profile_path) / 'metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                fingerprint = metadata.get('fingerprint', {})
                proxy_config = metadata.get('proxy_config', {})
            else:
                fingerprint = await self.fingerprint_generator.generate_fingerprint()
                proxy_config = {}
            
            # Create browser config
            browser_config = self._create_browser_config(proxy_config, fingerprint)
            browser_config.user_data_dir = profile_path
            browser_config.use_managed_browser = True
            browser_config.headless = False  # Must be visible for login
            
            async with AsyncWebCrawler(config=browser_config) as crawler:
                # Navigate to Facebook login page
                result = await crawler.arun("https://www.facebook.com/login")
                
                if not result.success:
                    return {
                        'success': False,
                        'message': 'Failed to load Facebook login page',
                        'error': result.error_message
                    }
                
                # TODO: Implement automated login logic here
                # For now, return success and let user login manually
                return {
                    'success': True,
                    'message': 'Facebook login page loaded. Please login manually.',
                    'manual_login_required': True
                }
                
        except Exception as e:
            logger.error(f"Facebook login failed: {e}")
            return {
                'success': False,
                'message': f'Facebook login failed: {e}',
                'error': str(e)
            }
    
    async def get_profile_cookies(self, profile_path: str) -> Optional[List[Dict]]:
        """Get cookies from profile"""
        try:
            # Load cookies from profile if available
            cookies_file = Path(profile_path) / 'cookies.json'
            if cookies_file.exists():
                with open(cookies_file, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logger.error(f"Failed to get cookies: {e}")
            return None
    
    async def save_profile_cookies(self, profile_path: str, cookies: List[Dict]) -> bool:
        """Save cookies to profile"""
        try:
            cookies_file = Path(profile_path) / 'cookies.json'
            with open(cookies_file, 'w') as f:
                json.dump(cookies, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Failed to save cookies: {e}")
            return False
    
    async def delete_profile(self, profile_path: str) -> bool:
        """Delete profile directory and all associated data"""
        try:
            import shutil
            if Path(profile_path).exists():
                shutil.rmtree(profile_path)
                logger.info(f"Profile deleted: {profile_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete profile: {e}")
            return False
