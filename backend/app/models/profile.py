"""
Profile models for Facebook Automation
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

Base = declarative_base()


class ProxyType(str, Enum):
    """Proxy types supported"""
    NO_PROXY = "no_proxy"
    HTTP = "http"
    HTTPS = "https"
    SOCKS5 = "socks5"
    SSH = "ssh"


class ProfileStatus(str, Enum):
    """Profile status"""
    CREATED = "created"
    ACTIVE = "active"
    LOGGED_IN = "logged_in"
    ERROR = "error"
    DISABLED = "disabled"


# SQLAlchemy Model
class Profile(Base):
    """Profile database model"""
    __tablename__ = "profiles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, index=True, nullable=False)
    profile_path = Column(String(500), nullable=False)
    
    # Proxy settings
    proxy_type = Column(String(50), default=ProxyType.NO_PROXY)
    proxy_host = Column(String(255), nullable=True)
    proxy_port = Column(Integer, nullable=True)
    proxy_username = Column(String(255), nullable=True)
    proxy_password = Column(String(255), nullable=True)
    
    # Browser fingerprint data
    fingerprint_data = Column(JSON, nullable=True)
    
    # Facebook login status
    facebook_logged_in = Column(Boolean, default=False)
    facebook_username = Column(String(255), nullable=True)
    cookies_data = Column(JSON, nullable=True)
    
    # Status and metadata
    status = Column(String(50), default=ProfileStatus.CREATED)
    last_used = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())


# Pydantic Models for API
class ProxyConfig(BaseModel):
    """Proxy configuration"""
    type: ProxyType = ProxyType.NO_PROXY
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None


class ProfileCreate(BaseModel):
    """Profile creation model"""
    name: str = Field(..., min_length=1, max_length=255)
    proxy_config: ProxyConfig = ProxyConfig()


class ProfileUpdate(BaseModel):
    """Profile update model"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    proxy_config: Optional[ProxyConfig] = None
    status: Optional[ProfileStatus] = None


class FacebookLoginRequest(BaseModel):
    """Facebook login request"""
    username: str = Field(..., min_length=1)
    password: str = Field(..., min_length=1)


class ProfileResponse(BaseModel):
    """Profile response model"""
    id: int
    name: str
    proxy_config: ProxyConfig
    facebook_logged_in: bool
    facebook_username: Optional[str]
    status: ProfileStatus
    last_used: Optional[datetime]
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

    @classmethod
    def from_db_model(cls, profile: Profile) -> "ProfileResponse":
        """Convert database model to response model"""
        proxy_config = ProxyConfig(
            type=profile.proxy_type or ProxyType.NO_PROXY,
            host=profile.proxy_host,
            port=profile.proxy_port,
            username=profile.proxy_username,
            password=profile.proxy_password
        )
        
        return cls(
            id=profile.id,
            name=profile.name,
            proxy_config=proxy_config,
            facebook_logged_in=profile.facebook_logged_in,
            facebook_username=profile.facebook_username,
            status=profile.status,
            last_used=profile.last_used,
            error_message=profile.error_message,
            created_at=profile.created_at,
            updated_at=profile.updated_at
        )


class ProfileTestResult(BaseModel):
    """Profile test result"""
    success: bool
    message: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    fingerprint: Optional[Dict[str, Any]] = None
