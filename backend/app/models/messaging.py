"""
Messaging models for Facebook Automation
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, ForeignKey, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

Base = declarative_base()


class MessagingTaskStatus(str, Enum):
    """Messaging task status"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class MessageStatus(str, Enum):
    """Individual message status"""
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    SKIPPED = "skipped"


class MessageType(str, Enum):
    """Message types"""
    TEXT = "text"
    IMAGE = "image"
    TEXT_WITH_IMAGE = "text_with_image"


# SQLAlchemy Models
class MessagingTask(Base):
    """Messaging task database model"""
    __tablename__ = "messaging_tasks"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(255), unique=True, index=True, nullable=False)
    
    # Task configuration
    name = Column(String(255), nullable=False)
    sender_profile_ids = Column(JSON, nullable=False)  # List of profile IDs
    recipient_list_file = Column(String(500), nullable=True)
    
    # Message configuration
    message_template = Column(Text, nullable=False)
    message_type = Column(String(50), default=MessageType.TEXT)
    image_paths = Column(JSON, nullable=True)  # List of image file paths
    
    # Threading configuration
    concurrent_threads = Column(Integer, default=1)
    messages_per_account_min = Column(Integer, default=1)
    messages_per_account_max = Column(Integer, default=10)
    delay_between_messages_min = Column(Integer, default=5)  # seconds
    delay_between_messages_max = Column(Integer, default=15)  # seconds
    
    # Options
    avoid_duplicate_uids = Column(Boolean, default=True)
    randomize_message = Column(Boolean, default=False)
    
    # Status and progress
    status = Column(String(50), default=MessagingTaskStatus.PENDING)
    progress = Column(Float, default=0.0)  # 0.0 to 100.0
    total_recipients = Column(Integer, default=0)
    messages_sent = Column(Integer, default=0)
    messages_failed = Column(Integer, default=0)
    messages_skipped = Column(Integer, default=0)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    messages = relationship("MessageLog", back_populates="task", cascade="all, delete-orphan")


class MessageLog(Base):
    """Individual message log model"""
    __tablename__ = "message_logs"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("messaging_tasks.id"), nullable=False)
    sender_profile_id = Column(Integer, ForeignKey("profiles.id"), nullable=False)
    
    # Recipient information
    recipient_uid = Column(String(255), nullable=False, index=True)
    recipient_name = Column(String(500), nullable=True)
    recipient_profile_url = Column(String(1000), nullable=True)
    
    # Message details
    message_content = Column(Text, nullable=False)
    message_type = Column(String(50), nullable=False)
    image_paths = Column(JSON, nullable=True)
    
    # Status and result
    status = Column(String(50), default=MessageStatus.PENDING)
    error_message = Column(Text, nullable=True)
    response_data = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    sent_at = Column(DateTime, nullable=True)
    
    # Relationships
    task = relationship("MessagingTask", back_populates="messages")


class RecipientList(Base):
    """Recipient list model for imported data"""
    __tablename__ = "recipient_lists"

    id = Column(Integer, primary_key=True, index=True)
    list_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    total_recipients = Column(Integer, default=0)
    
    # Metadata
    source_task_id = Column(String(255), nullable=True)  # If imported from scraping
    metadata = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())


# Pydantic Models for API
class MessagingConfig(BaseModel):
    """Messaging configuration"""
    name: str = Field(..., min_length=1, max_length=255)
    sender_profile_ids: List[int] = Field(..., min_items=1)
    recipient_list_file: Optional[str] = None
    recipient_uids: Optional[List[str]] = None  # Alternative to file
    
    message_template: str = Field(..., min_length=1)
    message_type: MessageType = MessageType.TEXT
    image_paths: Optional[List[str]] = None
    
    concurrent_threads: int = Field(default=1, ge=1, le=10)
    messages_per_account_min: int = Field(default=1, ge=1)
    messages_per_account_max: int = Field(default=10, ge=1)
    delay_between_messages_min: int = Field(default=5, ge=1)
    delay_between_messages_max: int = Field(default=15, ge=1)
    
    avoid_duplicate_uids: bool = True
    randomize_message: bool = False


class MessagingTaskCreate(BaseModel):
    """Messaging task creation model"""
    config: MessagingConfig


class MessagingTaskResponse(BaseModel):
    """Messaging task response model"""
    id: int
    task_id: str
    name: str
    sender_profile_ids: List[int]
    message_template: str
    message_type: MessageType
    concurrent_threads: int
    status: MessagingTaskStatus
    progress: float
    total_recipients: int
    messages_sent: int
    messages_failed: int
    messages_skipped: int
    error_message: Optional[str]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]

    class Config:
        from_attributes = True


class MessageLogResponse(BaseModel):
    """Message log response model"""
    id: int
    sender_profile_id: int
    recipient_uid: str
    recipient_name: Optional[str]
    message_content: str
    message_type: MessageType
    status: MessageStatus
    error_message: Optional[str]
    created_at: datetime
    sent_at: Optional[datetime]

    class Config:
        from_attributes = True


class MessagingResults(BaseModel):
    """Messaging results summary"""
    task_id: str
    status: MessagingTaskStatus
    total_recipients: int
    messages_sent: int
    messages_failed: int
    messages_skipped: int
    success_rate: float
    messages: List[MessageLogResponse]


class RecipientUpload(BaseModel):
    """Recipient upload response"""
    list_name: str
    total_recipients: int
    file_path: str
    preview: List[Dict[str, Any]]
