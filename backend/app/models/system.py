"""
System models for Facebook Automation
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

Base = declarative_base()


class LogLevel(str, Enum):
    """Log levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class TaskType(str, Enum):
    """Task types"""
    SCRAPING = "scraping"
    MESSAGING = "messaging"
    PROFILE_TEST = "profile_test"
    FACEBOOK_LOGIN = "facebook_login"


# SQLAlchemy Models
class SystemLog(Base):
    """System log database model"""
    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, index=True)
    level = Column(String(50), nullable=False, index=True)
    message = Column(Text, nullable=False)
    module = Column(String(255), nullable=True)
    function = Column(String(255), nullable=True)
    line_number = Column(Integer, nullable=True)
    
    # Context data
    user_id = Column(String(255), nullable=True)
    session_id = Column(String(255), nullable=True)
    task_id = Column(String(255), nullable=True)
    task_type = Column(String(50), nullable=True)
    
    # Additional data
    extra_data = Column(JSON, nullable=True)
    stack_trace = Column(Text, nullable=True)
    
    # Timestamp
    created_at = Column(DateTime, server_default=func.now(), index=True)


class SystemStats(Base):
    """System statistics model"""
    __tablename__ = "system_stats"

    id = Column(Integer, primary_key=True, index=True)
    
    # Performance metrics
    cpu_usage = Column(Float, nullable=True)
    memory_usage = Column(Float, nullable=True)
    disk_usage = Column(Float, nullable=True)
    
    # Application metrics
    active_profiles = Column(Integer, default=0)
    running_tasks = Column(Integer, default=0)
    total_scraped_users = Column(Integer, default=0)
    total_messages_sent = Column(Integer, default=0)
    
    # Browser metrics
    active_browsers = Column(Integer, default=0)
    browser_memory_usage = Column(Float, nullable=True)
    
    # Database metrics
    database_size = Column(Float, nullable=True)  # MB
    total_records = Column(Integer, default=0)
    
    # Timestamp
    recorded_at = Column(DateTime, server_default=func.now(), index=True)


class AppSettings(Base):
    """Application settings model"""
    __tablename__ = "app_settings"

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(255), unique=True, nullable=False, index=True)
    value = Column(JSON, nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)
    
    # Metadata
    is_system = Column(Boolean, default=False)  # System vs user settings
    requires_restart = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())


# Pydantic Models for API
class SystemLogResponse(BaseModel):
    """System log response model"""
    id: int
    level: LogLevel
    message: str
    module: Optional[str]
    function: Optional[str]
    line_number: Optional[int]
    user_id: Optional[str]
    session_id: Optional[str]
    task_id: Optional[str]
    task_type: Optional[TaskType]
    extra_data: Optional[Dict[str, Any]]
    created_at: datetime

    class Config:
        from_attributes = True


class SystemStatsResponse(BaseModel):
    """System statistics response model"""
    id: int
    cpu_usage: Optional[float]
    memory_usage: Optional[float]
    disk_usage: Optional[float]
    active_profiles: int
    running_tasks: int
    total_scraped_users: int
    total_messages_sent: int
    active_browsers: int
    browser_memory_usage: Optional[float]
    database_size: Optional[float]
    total_records: int
    recorded_at: datetime

    class Config:
        from_attributes = True


class AppSettingResponse(BaseModel):
    """App setting response model"""
    id: int
    key: str
    value: Any
    description: Optional[str]
    category: Optional[str]
    is_system: bool
    requires_restart: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AppSettingUpdate(BaseModel):
    """App setting update model"""
    value: Any
    description: Optional[str] = None


class SystemStatusResponse(BaseModel):
    """System status response"""
    status: str
    version: str
    uptime: float  # seconds
    stats: SystemStatsResponse
    active_tasks: Dict[str, int]
    health_checks: Dict[str, bool]
