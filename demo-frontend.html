<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Automation Desktop - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background: #f5f5f5;
            overflow-x: hidden;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            animation: fadeOut 3s ease-in-out 2s forwards;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-logo {
            font-size: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        .loading-content h2 {
            color: white;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 300;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        .loading-content p {
            color: rgba(255, 255, 255, 0.8);
            margin-top: 20px;
            font-size: 16px;
        }

        /* Main App Layout */
        .app-container {
            display: flex;
            min-height: 100vh;
            opacity: 0;
            animation: fadeIn 1s ease-in 3s forwards;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            box-shadow: 4px 0 20px rgba(0,0,0,0.1);
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 1000;
        }

        .sidebar-logo {
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            margin: 16px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            font-size: 36px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .logo-text {
            color: white;
            line-height: 1.2;
        }

        .logo-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }

        .logo-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin: 0;
        }

        .nav-menu {
            padding: 20px 16px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            margin: 6px 0;
            border-radius: 12px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(4px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .nav-icon {
            font-size: 18px;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 280px;
            right: 0;
            height: 80px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            z-index: 999;
            border-bottom: 1px solid rgba(0,0,0,0.06);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .header-title {
            font-size: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-badge {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: rgba(0,0,0,0.04);
            border-radius: 20px;
            font-size: 12px;
        }

        .status-online {
            color: #52c41a;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            margin-top: 80px;
            padding: 24px;
            min-height: calc(100vh - 80px);
        }

        .content-wrapper {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Dashboard Hero */
        .dashboard-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 32px;
            color: white;
            position: relative;
            overflow: hidden;
            margin-bottom: 32px;
        }

        .hero-bg-pattern {
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 36px;
            margin-bottom: 8px;
        }

        .hero-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 12px;
        }

        .hero-time {
            font-size: 14px;
            opacity: 0.8;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .stat-card.gradient-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-card.gradient-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .stat-card.gradient-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .stat-card.gradient-4 {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .stat-icon {
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 60px;
            opacity: 0.2;
        }

        .stat-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 12px;
        }

        .stat-description {
            font-size: 14px;
            opacity: 0.9;
        }

        /* Animations */
        @keyframes fadeOut {
            to { opacity: 0; visibility: hidden; }
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .header {
                left: 0;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">📱</div>
            <h2>Facebook Automation Desktop</h2>
            <div class="loading-spinner"></div>
            <p>Initializing application...</p>
        </div>
    </div>

    <!-- Main App -->
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-logo">
                <div class="logo-container">
                    <div class="logo-icon">📱</div>
                    <div class="logo-text">
                        <div class="logo-title">Facebook</div>
                        <div class="logo-subtitle">Automation</div>
                    </div>
                </div>
            </div>

            <nav class="nav-menu">
                <a href="#" class="nav-item active">
                    <span class="nav-icon">📊</span>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-icon">👤</span>
                    <span>Profile Manager</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-icon">🔍</span>
                    <span>Facebook Scraping</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-icon">💬</span>
                    <span>Bulk Messaging</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-icon">⚙️</span>
                    <span>Settings</span>
                </a>
            </nav>
        </div>

        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <h1 class="header-title">Facebook Automation Desktop</h1>
            </div>
            <div class="header-right">
                <div class="status-badge">
                    <span class="status-online">●</span>
                    <span>System Online</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-wrapper">
                <!-- Dashboard Hero -->
                <div class="dashboard-hero">
                    <div class="hero-bg-pattern"></div>
                    <div class="hero-content">
                        <h1 class="hero-title">🚀 Facebook Automation</h1>
                        <p class="hero-subtitle">Complete automation solution for Facebook marketing</p>
                        <div class="hero-time">
                            🕐 <span id="current-time"></span>
                        </div>
                    </div>
                </div>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card gradient-1">
                        <div class="stat-icon">👤</div>
                        <div class="stat-title">Browser Profiles</div>
                        <div class="stat-value">12</div>
                        <div class="stat-description">✓ 8 logged into Facebook</div>
                    </div>

                    <div class="stat-card gradient-2">
                        <div class="stat-icon">🔍</div>
                        <div class="stat-title">Scraping Tasks</div>
                        <div class="stat-value">45</div>
                        <div class="stat-description">🏆 38 completed successfully</div>
                    </div>

                    <div class="stat-card gradient-3">
                        <div class="stat-icon">💬</div>
                        <div class="stat-title">Messages Sent</div>
                        <div class="stat-value">2,847</div>
                        <div class="stat-description">🔥 23 campaigns completed</div>
                    </div>

                    <div class="stat-card gradient-4">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-title">Success Rate</div>
                        <div class="stat-value">94.2%</div>
                        <div class="stat-description">⚡ Average campaign performance</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }

        // Update time every second
        setInterval(updateTime, 1000);
        updateTime();

        // Add some interactivity
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
            });
        });

        // Add hover effects to stat cards
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-4px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
